'use client';

import React from 'react';
import { cn } from '@/lib/utils';

/**
 * 平滑评论更新器 - 处理动画和过渡效果
 */
export class SmoothCommentUpdater {
  private static instance: SmoothCommentUpdater;

  static getInstance(): SmoothCommentUpdater {
    if (!SmoothCommentUpdater.instance) {
      SmoothCommentUpdater.instance = new SmoothCommentUpdater();
    }
    return SmoothCommentUpdater.instance;
  }

  // 播放提交动画
  playSubmitAnimation(commentId: string): void {
    const element = document.querySelector(`[data-comment-id="${commentId}"]`) as HTMLElement;
    if (!element) return;

    // 添加提交中的视觉反馈
    element.style.transition = 'all 0.3s ease';
    element.style.transform = 'scale(0.98)';
    element.style.opacity = '0.7';
    element.classList.add('animate-pulse');

    // 添加加载指示器
    this.addLoadingIndicator(element);
  }

  // 播放成功动画
  playSuccessAnimation(commentId: string): void {
    const element = document.querySelector(`[data-comment-id="${commentId}"]`) as HTMLElement;
    if (!element) return;

    // 移除加载状态
    this.removeLoadingIndicator(element);
    element.classList.remove('animate-pulse');

    // 成功动画序列
    element.style.transform = 'scale(1.02)';
    element.style.opacity = '1';
    element.style.backgroundColor = '#f0fdf4'; // 淡绿色
    element.style.borderColor = '#22c55e';

    // 添加成功粒子效果
    this.createSuccessParticles(element);

    // 3秒后恢复正常
    setTimeout(() => {
      element.style.backgroundColor = '';
      element.style.borderColor = '';
      element.style.transform = 'scale(1)';
      element.style.transition = '';
    }, 3000);
  }

  // 播放错误动画
  playErrorAnimation(commentId: string): void {
    const element = document.querySelector(`[data-comment-id="${commentId}"]`) as HTMLElement;
    if (!element) return;

    // 移除加载状态
    this.removeLoadingIndicator(element);
    element.classList.remove('animate-pulse');

    // 错误动画 - 摇摆效果
    element.style.backgroundColor = '#fef2f2'; // 淡红色
    element.style.borderColor = '#ef4444';
    
    // CSS动画摇摆
    element.style.animation = 'shake 0.5s ease-in-out';

    // 2秒后恢复
    setTimeout(() => {
      element.style.backgroundColor = '';
      element.style.borderColor = '';
      element.style.animation = '';
      element.style.transform = 'scale(1)';
      element.style.opacity = '1';
    }, 2000);
  }

  // 平滑滚动到评论
  smoothScrollToComment(commentId: string, behavior: ScrollBehavior = 'smooth'): void {
    const element = document.querySelector(`[data-comment-id="${commentId}"]`);
    if (!element) return;

    // 计算滚动位置，留出一些顶部空间
    const elementRect = element.getBoundingClientRect();
    const absoluteElementTop = elementRect.top + window.pageYOffset;
    const offset = 100; // 距离顶部的偏移量

    window.scrollTo({
      top: absoluteElementTop - offset,
      behavior,
    });

    // 添加临时高亮效果
    this.addTemporaryHighlight(element as HTMLElement);
  }

  // 添加临时高亮效果
  private addTemporaryHighlight(element: HTMLElement): void {
    const originalBackground = element.style.backgroundColor;
    const originalTransition = element.style.transition;

    element.style.transition = 'background-color 0.5s ease';
    element.style.backgroundColor = '#dbeafe'; // 淡蓝色高亮

    setTimeout(() => {
      element.style.backgroundColor = originalBackground;
      setTimeout(() => {
        element.style.transition = originalTransition;
      }, 500);
    }, 2000);
  }

  // 添加加载指示器
  private addLoadingIndicator(element: HTMLElement): void {
    const existing = element.querySelector('.loading-indicator');
    if (existing) return;

    const indicator = document.createElement('div');
    indicator.className = 'loading-indicator absolute top-2 right-2 w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin z-10';
    
    // 确保父元素有相对定位
    if (getComputedStyle(element).position === 'static') {
      element.style.position = 'relative';
    }
    
    element.appendChild(indicator);
  }

  // 移除加载指示器
  private removeLoadingIndicator(element: HTMLElement): void {
    const indicator = element.querySelector('.loading-indicator');
    if (indicator) {
      indicator.remove();
    }
  }

  // 创建成功粒子效果
  private createSuccessParticles(element: HTMLElement): void {
    const rect = element.getBoundingClientRect();
    const particles = 6;

    for (let i = 0; i < particles; i++) {
      const particle = document.createElement('div');
      particle.className = 'fixed pointer-events-none z-50';
      particle.style.cssText = `
        width: 6px;
        height: 6px;
        background: #22c55e;
        border-radius: 50%;
        left: ${rect.left + rect.width / 2}px;
        top: ${rect.top + rect.height / 2}px;
        animation: particle-${i} 1s ease-out forwards;
      `;

      // 动态创建粒子动画
      const angle = (i / particles) * 360;
      const distance = 30 + Math.random() * 20;
      const keyframes = `
        @keyframes particle-${i} {
          0% {
            opacity: 1;
            transform: translate(0, 0) scale(1);
          }
          100% {
            opacity: 0;
            transform: translate(
              ${Math.cos(angle * Math.PI / 180) * distance}px,
              ${Math.sin(angle * Math.PI / 180) * distance}px
            ) scale(0);
          }
        }
      `;

      // 添加样式表
      const style = document.createElement('style');
      style.textContent = keyframes;
      document.head.appendChild(style);

      document.body.appendChild(particle);

      // 清理
      setTimeout(() => {
        particle.remove();
        style.remove();
      }, 1000);
    }
  }

  // 智能滚动管理 - 避免打断用户阅读
  smartScrollToNewComment(commentId: string): void {
    const userScrollPosition = window.pageYOffset;
    const documentHeight = document.documentElement.scrollHeight;
    const windowHeight = window.innerHeight;
    
    // 如果用户在页面底部附近，则滚动到新评论
    const nearBottom = userScrollPosition + windowHeight > documentHeight - 300;
    
    if (nearBottom) {
      setTimeout(() => {
        this.smoothScrollToComment(commentId, 'smooth');
      }, 500); // 延迟滚动，让用户先看到成功动画
    } else {
      // 否则只显示通知，不强制滚动
      this.showScrollNotification(commentId);
    }
  }

  // 显示滚动通知
  private showScrollNotification(commentId: string): void {
    const notification = document.createElement('div');
    notification.className = 'fixed bottom-4 left-1/2 transform -translate-x-1/2 bg-blue-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 cursor-pointer transition-all duration-300';
    notification.innerHTML = `
      <div class="flex items-center gap-2">
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11l5-5m0 0l5 5m-5-5v12"></path>
        </svg>
        <span>评论发表成功！点击查看</span>
      </div>
    `;

    notification.onclick = () => {
      this.smoothScrollToComment(commentId);
      notification.remove();
    };

    document.body.appendChild(notification);

    // 5秒后自动消失
    setTimeout(() => {
      notification.style.opacity = '0';
      notification.style.transform = 'translate(-50%, 100%)';
      setTimeout(() => {
        if (notification.parentNode) {
          notification.remove();
        }
      }, 300);
    }, 5000);
  }

  // 保持用户上下文
  preserveUserContext(): {
    scrollPosition: number;
    focusedElement: Element | null;
    selectedText: string;
  } {
    return {
      scrollPosition: window.pageYOffset,
      focusedElement: document.activeElement,
      selectedText: window.getSelection()?.toString() || '',
    };
  }

  // 恢复用户上下文
  restoreUserContext(context: {
    scrollPosition: number;
    focusedElement: Element | null;
    selectedText: string;
  }, shouldMaintainScroll: boolean = false): void {
    if (shouldMaintainScroll) {
      window.scrollTo({
        top: context.scrollPosition,
        behavior: 'auto',
      });
    }

    // 恢复焦点（如果合适）
    if (context.focusedElement && this.shouldRestoreFocus(context.focusedElement)) {
      (context.focusedElement as HTMLElement).focus();
    }
  }

  // 判断是否应该恢复焦点
  private shouldRestoreFocus(element: Element): boolean {
    // 不恢复输入框焦点，因为用户可能已经完成了输入
    if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
      return false;
    }

    // 恢复按钮或链接焦点
    return ['BUTTON', 'A'].includes(element.tagName);
  }
}

// React Hook 用于平滑更新
export function useSmoothCommentUpdater() {
  const updater = React.useMemo(() => SmoothCommentUpdater.getInstance(), []);

  return {
    playSubmitAnimation: updater.playSubmitAnimation.bind(updater),
    playSuccessAnimation: updater.playSuccessAnimation.bind(updater),
    playErrorAnimation: updater.playErrorAnimation.bind(updater),
    smoothScrollToComment: updater.smoothScrollToComment.bind(updater),
    smartScrollToNewComment: updater.smartScrollToNewComment.bind(updater),
    preserveUserContext: updater.preserveUserContext.bind(updater),
    restoreUserContext: updater.restoreUserContext.bind(updater),
  };
}

// CSS 动画样式组件
export function SmoothAnimationStyles() {
  return (
    <style jsx global>{`
      @keyframes shake {
        0%, 100% { transform: translateX(0); }
        10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
        20%, 40%, 60%, 80% { transform: translateX(2px); }
      }

      @keyframes pulse-success {
        0% { background-color: #f0fdf4; }
        50% { background-color: #dcfce7; }
        100% { background-color: #f0fdf4; }
      }

      @keyframes slide-in-up {
        from {
          opacity: 0;
          transform: translateY(20px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .comment-optimistic {
        animation: slide-in-up 0.3s ease-out;
      }

      .comment-success {
        animation: pulse-success 0.5s ease-out;
      }

      .comment-error {
        animation: shake 0.5s ease-in-out;
      }

      /* 平滑过渡效果 */
      .comment-item {
        transition: all 0.3s ease;
      }

      .comment-item:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      /* 新评论高亮效果 */
      .comment-highlight {
        background: linear-gradient(90deg, #dbeafe, #f0f9ff);
        border-left: 4px solid #3b82f6;
        animation: slide-in-up 0.5s ease-out;
      }

      /* 加载状态 */
      .comment-submitting {
        opacity: 0.7;
        pointer-events: none;
      }

      .comment-submitting::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(59, 130, 246, 0.1);
        border-radius: 8px;
      }
    `}</style>
  );
}