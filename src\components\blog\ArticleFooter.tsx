import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { BlogPost } from '@/types';
import { Tag } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ArticleFooterProps {
  post: BlogPost;
  relatedPosts: BlogPost[];
  className?: string;
}

/**
 * 文章底部组件 - SSR优化版本
 * 基于01-frontend-design-rules.md的博客设计规范
 * 移除客户端交互，专注于SEO友好的内容展示
 */
export function ArticleFooter({ post, relatedPosts, className }: ArticleFooterProps) {



  return (
    <footer className={cn('mt-16 space-y-8', className)}>
      {/* 分隔线 */}
      <div className="flex items-center justify-center my-12">
        <div className="text-gray-400 dark:text-gray-600 text-2xl tracking-widest">✦ ✦ ✦</div>
      </div>

      {/* 标签区域 */}
      {post.tags && post.tags.length > 0 && (
        <div className="space-y-4">
          <h3 className="flex items-center gap-2 text-lg font-semibold text-gray-900 dark:text-gray-100">
            <Tag className="w-5 h-5" />
            标签
          </h3>
          <div className="flex flex-wrap gap-2">
            {post.tags.map((tag) => (
              <Link
                key={tag}
                href={`/${post.locale}/blog?tag=${encodeURIComponent(tag)}`}
                className="inline-flex items-center px-3 py-1.5 text-sm font-medium
                  bg-gray-100 dark:bg-gray-800 
                  text-gray-700 dark:text-gray-300
                  border border-gray-200 dark:border-gray-700
                  rounded-full transition-all duration-200
                  hover:bg-gray-200 dark:hover:bg-gray-700
                  hover:border-gray-300 dark:hover:border-gray-600
                  hover:transform hover:scale-105"
              >
                #{tag}
              </Link>
            ))}
          </div>
        </div>
      )}



      {/* 作者简介卡片 */}
      <div className="bg-gray-50 dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700">
        <div className="flex items-start gap-4">
          {/* 作者头像 */}
          <div className="flex-shrink-0">
            <div className="w-16 h-16 rounded-full bg-gradient-to-br from-gray-400 to-gray-600 flex items-center justify-center text-white font-bold text-xl">
              A
            </div>
          </div>
          
          {/* 作者信息 */}
          <div className="flex-1">
            <h4 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-2">
              作者
            </h4>
            <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
              专注于玄学领域的内容创作，致力于为读者提供有价值的洞察和指导。
            </p>
          </div>
        </div>
      </div>

      {/* 相关文章推荐 */}
      {relatedPosts && relatedPosts.length > 0 && (
        <div className="space-y-6">
          <h3 className="text-2xl font-bold text-gray-900 dark:text-gray-100 text-center">
            相关文章
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {relatedPosts.slice(0, 3).map((relatedPost) => (
              <Link
                key={relatedPost.id}
                href={`/${relatedPost.locale}/blog/${relatedPost.category}/${relatedPost.slug}`}
                className="group block bg-white dark:bg-gray-800 rounded-xl overflow-hidden
                  border border-gray-200 dark:border-gray-700
                  transition-all duration-300
                  hover:transform hover:scale-105 hover:shadow-lg
                  hover:border-gray-300 dark:hover:border-gray-600"
              >
                {/* 文章图片 */}
                {relatedPost.coverImage && (
                  <div className="aspect-[16/9] overflow-hidden">
                    <Image
                      src={relatedPost.coverImage}
                      alt={relatedPost.title}
                      width={400}
                      height={225}
                      className="w-full h-full object-cover transition-transform duration-300
                        group-hover:scale-110"
                    />
                  </div>
                )}
                
                {/* 文章内容 */}
                <div className="p-4">
                  <div className="text-xs font-semibold text-gray-600 dark:text-gray-400 uppercase tracking-wide mb-2">
                    {relatedPost.category}
                  </div>
                  <h4 className="text-lg font-bold text-gray-900 dark:text-gray-100 mb-2 line-clamp-2
                    group-hover:text-gray-700 dark:group-hover:text-gray-300 transition-colors">
                    {relatedPost.title}
                  </h4>
                  {relatedPost.excerpt && (
                    <p className="text-sm text-gray-600 dark:text-gray-300 line-clamp-3 mb-3">
                      {relatedPost.excerpt}
                    </p>
                  )}
                  <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                    <span>{relatedPost.readingTime} min read</span>
                    <span>{relatedPost.viewCount} views</span>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      )}

      {/* 移除重复的文章互动统计，这些信息已经在右侧浮动栏显示 */}
    </footer>
  );
}
