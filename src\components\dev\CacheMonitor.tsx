'use client';

import React, { useState, useEffect } from 'react';
import { commentCache } from '@/lib/cache/comment-cache';

interface CacheMonitorProps {
  enabled?: boolean;
}

/**
 * 缓存性能监控组件 - 仅在开发环境显示
 */
export function CacheMonitor({ enabled = process.env.NODE_ENV === 'development' }: CacheMonitorProps) {
  const [stats, setStats] = useState({
    memorySize: 0,
    hitRate: 0,
    totalRequests: 0,
    cacheHits: 0,
  });

  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    if (!enabled) return;

    const updateStats = () => {
      const cacheStats = commentCache.getCacheStats();
      setStats(prev => ({
        ...prev,
        memorySize: cacheStats.memorySize,
        hitRate: cacheStats.hitRate,
      }));
    };

    // 每秒更新一次统计信息
    const interval = setInterval(updateStats, 1000);

    return () => clearInterval(interval);
  }, [enabled]);

  if (!enabled) return null;

  return (
    <>
      {/* 悬浮按钮 */}
      <button
        onClick={() => setIsVisible(!isVisible)}
        className="fixed bottom-4 right-4 z-50 w-12 h-12 bg-blue-500 text-white rounded-full shadow-lg hover:bg-blue-600 transition-colors"
        title="缓存监控"
      >
        📊
      </button>

      {/* 监控面板 */}
      {isVisible && (
        <div className="fixed bottom-20 right-4 z-50 w-80 bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              缓存监控
            </h3>
            <button
              onClick={() => setIsVisible(false)}
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
            >
              ✕
            </button>
          </div>

          <div className="space-y-3">
            {/* 内存缓存大小 */}
            <div className="flex justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">内存缓存项数:</span>
              <span className="text-sm font-mono text-gray-900 dark:text-gray-100">
                {stats.memorySize}
              </span>
            </div>

            {/* 命中率 */}
            <div className="flex justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">缓存命中率:</span>
              <span className="text-sm font-mono text-gray-900 dark:text-gray-100">
                {(stats.hitRate * 100).toFixed(1)}%
              </span>
            </div>

            {/* 命中率可视化 */}
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div
                className="bg-green-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${stats.hitRate * 100}%` }}
              />
            </div>

            {/* 操作按钮 */}
            <div className="flex gap-2 pt-2">
              <button
                onClick={() => {
                  localStorage.clear();
                  console.log('LocalStorage cleared');
                }}
                className="px-3 py-1 text-xs bg-yellow-500 text-white rounded hover:bg-yellow-600 transition-colors"
              >
                清除LS
              </button>
              <button
                onClick={() => {
                  // 这里可以添加清除IndexedDB的逻辑
                  console.log('Cache optimization triggered');
                }}
                className="px-3 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
              >
                优化缓存
              </button>
            </div>

            {/* 缓存状态指示器 */}
            <div className="pt-2 border-t border-gray-200 dark:border-gray-700">
              <div className="flex items-center gap-2">
                <div className={`w-2 h-2 rounded-full ${stats.hitRate > 0.8 ? 'bg-green-500' : stats.hitRate > 0.5 ? 'bg-yellow-500' : 'bg-red-500'}`} />
                <span className="text-xs text-gray-600 dark:text-gray-400">
                  {stats.hitRate > 0.8 ? '缓存表现优秀' : stats.hitRate > 0.5 ? '缓存表现良好' : '缓存需要优化'}
                </span>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}

// 缓存性能统计 Hook
export function useCacheStats() {
  const [stats, setStats] = useState({
    memorySize: 0,
    hitRate: 0,
    performance: {
      averageLoadTime: 0,
      cacheLoadTime: 0,
      networkLoadTime: 0,
    }
  });

  useEffect(() => {
    const updateStats = () => {
      const cacheStats = commentCache.getCacheStats();
      setStats(prev => ({
        ...prev,
        memorySize: cacheStats.memorySize,
        hitRate: cacheStats.hitRate,
      }));
    };

    updateStats();
    const interval = setInterval(updateStats, 5000); // 每5秒更新

    return () => clearInterval(interval);
  }, []);

  return stats;
}

// 性能测试组件
export function CachePerformanceTest() {
  const [results, setResults] = useState<{
    cacheTime: number;
    networkTime: number;
    improvement: number;
  } | null>(null);

  const runTest = async () => {
    const testPostId = 'test-post-id';
    
    // 测试网络请求时间
    const networkStart = performance.now();
    try {
      await fetch(`/api/blog/${testPostId}/comments`);
    } catch (error) {
      // 忽略错误，只测试时间
    }
    const networkTime = performance.now() - networkStart;

    // 测试缓存请求时间
    const cacheStart = performance.now();
    await commentCache.getComments(testPostId);
    const cacheTime = performance.now() - cacheStart;

    const improvement = ((networkTime - cacheTime) / networkTime) * 100;

    setResults({
      cacheTime,
      networkTime,
      improvement,
    });
  };

  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className="p-4 bg-gray-100 dark:bg-gray-800 rounded-lg">
      <h4 className="text-lg font-semibold mb-4">缓存性能测试</h4>
      
      <button
        onClick={runTest}
        className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors mb-4"
      >
        运行性能测试
      </button>

      {results && (
        <div className="space-y-2 text-sm">
          <div>网络请求时间: {results.networkTime.toFixed(2)}ms</div>
          <div>缓存请求时间: {results.cacheTime.toFixed(2)}ms</div>
          <div className="font-semibold text-green-600">
            性能提升: {results.improvement.toFixed(1)}%
          </div>
        </div>
      )}
    </div>
  );
}