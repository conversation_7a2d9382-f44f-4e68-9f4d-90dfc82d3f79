import React from 'react';
import { Eye, Heart, MessageCircle, Share2 } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ArticleStatsProps {
  views: number;
  likes: number;
  comments: number;
  shares: number;
  className?: string;
  variant?: 'inline' | 'compact' | 'detailed';
}

/**
 * 文章统计组件 - SSR友好版本
 * 纯展示组件，确保统计数据对搜索引擎可见
 * 提供社会证明信号，提升SEO价值
 */
export function ArticleStats({
  views,
  likes,
  comments,
  shares,
  className,
  variant = 'inline'
}: ArticleStatsProps) {
  const stats = [
    {
      icon: Eye,
      label: '浏览',
      value: views,
      color: 'text-gray-500 dark:text-gray-400'
    },
    {
      icon: Heart,
      label: '点赞',
      value: likes,
      color: 'text-red-500 dark:text-red-400'
    },
    {
      icon: MessageCircle,
      label: '评论',
      value: comments,
      color: 'text-blue-500 dark:text-blue-400'
    },
    {
      icon: Share2,
      label: '分享',
      value: shares,
      color: 'text-green-500 dark:text-green-400'
    }
  ];

  if (variant === 'compact') {
    return (
      <div className={cn('flex items-center gap-4 text-sm', className)}>
        {stats.map((stat, index) => {
          const IconComponent = stat.icon;
          return (
            <div key={index} className="flex items-center gap-1">
              <IconComponent className={cn('w-4 h-4', stat.color)} />
              <span className="text-gray-600 dark:text-gray-300 font-medium">
                {stat.value}
              </span>
            </div>
          );
        })}
      </div>
    );
  }

  if (variant === 'detailed') {
    return (
      <div className={cn('grid grid-cols-2 md:grid-cols-4 gap-4', className)}>
        {stats.map((stat, index) => {
          const IconComponent = stat.icon;
          return (
            <div key={index} className="text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <IconComponent className={cn('w-6 h-6 mx-auto mb-2', stat.color)} />
              <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {stat.value.toLocaleString()}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                {stat.label}
              </div>
            </div>
          );
        })}
      </div>
    );
  }

  // Default inline variant
  return (
    <div className={cn('flex items-center justify-between py-4 border-y border-gray-200 dark:border-gray-700', className)}>
      <div className="flex items-center gap-6">
        {stats.map((stat, index) => {
          const IconComponent = stat.icon;
          return (
            <div key={index} className="flex items-center gap-2">
              <IconComponent className={cn('w-5 h-5', stat.color)} />
              <span className="text-gray-900 dark:text-gray-100 font-medium">
                {stat.label}
              </span>
              <span className="text-gray-600 dark:text-gray-300">
                ({stat.value.toLocaleString()})
              </span>
            </div>
          );
        })}
      </div>
    </div>
  );
}

/**
 * 格式化数字显示
 * 将大数字转换为更友好的显示格式
 */
export function formatStatNumber(num: number): string {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M';
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K';
  }
  return num.toString();
}

/**
 * 带格式化的文章统计组件
 */
export function ArticleStatsFormatted({
  views,
  likes,
  comments,
  shares,
  className,
  variant = 'inline'
}: ArticleStatsProps) {
  return (
    <ArticleStats
      views={views}
      likes={likes}
      comments={comments}
      shares={shares}
      className={className}
      variant={variant}
    />
  );
}
