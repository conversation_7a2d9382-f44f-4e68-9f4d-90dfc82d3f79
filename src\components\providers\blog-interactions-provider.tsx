'use client';

import React, { createContext, useContext, useReducer, ReactNode, useEffect } from 'react';
import { commentCache, UserBehaviorAnalyzer } from '@/lib/cache/comment-cache';
import { realtimeCommentManager } from '@/lib/realtime/websocket-manager';

// 评论数据类型
interface Comment {
  id: string;
  content: string;
  userId?: string;
  userName?: string;
  userAvatar?: string;
  postId: string;
  parentId?: string;
  isApproved: boolean;
  createdAt: Date | string;
  updatedAt: Date | string;
  replies?: Comment[];
  likeCount?: number;
  isLiked?: boolean;
  guestName?: string;
  guestEmail?: string;
  isOptimistic?: boolean; // 标记乐观更新的评论
}

// 评论点赞存储类型
interface LikedCommentsStorage {
  [commentId: string]: {
    isLiked: boolean;
    timestamp: number;
  };
}

// 交互统计数据类型
interface InteractionStats {
  views: number;
  likes: number;
  shares: number;
  comments: number;
}

// 用户状态类型
interface UserState {
  isLiked: boolean;
  isBookmarked: boolean;
}

// 评论状态类型
interface CommentsState {
  list: Comment[];
  likedComments: LikedCommentsStorage;
  loading: boolean;
  submitting: boolean;
  newCommentId: string | null;
  replyingTo: string | null;
  // 实时状态
  realtimeEnabled: boolean;
  connectionState: string;
  onlineUserCount: number;
  typingUsers: Array<{
    userId: string;
    userName: string;
    isTyping: boolean;
    timestamp: number;
  }>;
}

// 完整的交互状态
interface BlogInteractionsState {
  stats: InteractionStats;
  userState: UserState;
  comments: CommentsState;
  loading: {
    like: boolean;
    bookmark: boolean;
    commentSubmit: boolean;
    commentLike: boolean;
  };
}

// Action 类型
type BlogInteractionsAction =
  | { type: 'SET_LOADING'; payload: { action: 'like' | 'bookmark' | 'commentSubmit' | 'commentLike'; loading: boolean } }
  | { type: 'UPDATE_LIKE'; payload: { isLiked: boolean; likeCount?: number } }
  | { type: 'UPDATE_BOOKMARK'; payload: { isBookmarked: boolean } }
  | { type: 'UPDATE_STATS'; payload: Partial<InteractionStats> }
  | { type: 'INCREMENT_SHARE_COUNT' }
  | { type: 'INCREMENT_VIEW_COUNT' }
  | { type: 'SET_COMMENTS'; payload: { comments: Comment[] } }
  | { type: 'ADD_COMMENT'; payload: { comment: Comment } }
  | { type: 'UPDATE_COMMENT'; payload: { commentId: string; updates: Partial<Comment> } }
  | { type: 'REMOVE_COMMENT'; payload: { commentId: string } }
  | { type: 'SET_COMMENT_LIKE'; payload: { commentId: string; isLiked: boolean; likeCount?: number } }
  | { type: 'SET_LIKED_COMMENTS'; payload: { likedComments: LikedCommentsStorage } }
  | { type: 'SET_NEW_COMMENT_ID'; payload: { commentId: string | null } }
  | { type: 'SET_REPLYING_TO'; payload: { commentId: string | null } }
  | { type: 'SET_COMMENTS_LOADING'; payload: { loading: boolean } }
  | { type: 'SET_REALTIME_STATE'; payload: { connectionState: string; onlineUserCount?: number } }
  | { type: 'SET_TYPING_USERS'; payload: { typingUsers: Array<{ userId: string; userName: string; isTyping: boolean; timestamp: number }> } }
  | { type: 'ADD_REALTIME_COMMENT'; payload: { comment: Comment } };

// Context 类型
interface BlogInteractionsContextType {
  state: BlogInteractionsState;
  actions: {
    handleLike: () => Promise<void>;
    handleBookmark: () => Promise<void>;
    handleShare: (platform?: string) => Promise<void>;
    updateStats: (stats: Partial<InteractionStats>) => void;
    // 评论相关actions
    setComments: (comments: Comment[]) => void;
    addComment: (comment: Comment) => void;
    updateComment: (commentId: string, updates: Partial<Comment>) => void;
    removeComment: (commentId: string) => void;
    handleCommentLike: (commentId: string) => Promise<void>;
    submitComment: (content: string, parentId?: string, guestName?: string, guestEmail?: string) => Promise<Comment>;
    setReplyingTo: (commentId: string | null) => void;
    loadComments: () => Promise<void>;
    // 实时功能
    sendTypingIndicator: (isTyping: boolean) => void;
    connectRealtime: () => Promise<void>;
    disconnectRealtime: () => void;
  };
}

// 辅助函数：在评论树中查找评论
function findCommentInTree(comments: Comment[], commentId: string): Comment | null {
  for (const comment of comments) {
    if (comment.id === commentId) {
      return comment;
    }
    if (comment.replies) {
      const found = findCommentInTree(comment.replies, commentId);
      if (found) return found;
    }
  }
  return null;
}

// 辅助函数：在评论树中更新评论
function updateCommentInTree(comments: Comment[], commentId: string, updates: Partial<Comment>): Comment[] {
  return comments.map(comment => {
    if (comment.id === commentId) {
      return { ...comment, ...updates };
    }
    if (comment.replies) {
      return {
        ...comment,
        replies: updateCommentInTree(comment.replies, commentId, updates),
      };
    }
    return comment;
  });
}

// 辅助函数：从评论树中移除评论
function removeCommentFromTree(comments: Comment[], commentId: string): Comment[] {
  return comments.filter(comment => {
    if (comment.id === commentId) {
      return false;
    }
    if (comment.replies) {
      comment.replies = removeCommentFromTree(comment.replies, commentId);
    }
    return true;
  });
}

// 辅助函数：添加回复到评论树
function addReplyToCommentTree(comments: Comment[], parentId: string, newReply: Comment): Comment[] {
  return comments.map(comment => {
    if (comment.id === parentId) {
      return {
        ...comment,
        replies: [...(comment.replies || []), newReply],
      };
    }
    if (comment.replies) {
      return {
        ...comment,
        replies: addReplyToCommentTree(comment.replies, parentId, newReply),
      };
    }
    return comment;
  });
}

// Reducer 函数
function blogInteractionsReducer(
  state: BlogInteractionsState,
  action: BlogInteractionsAction
): BlogInteractionsState {
  switch (action.type) {
    case 'SET_LOADING':
      return {
        ...state,
        loading: {
          ...state.loading,
          [action.payload.action]: action.payload.loading,
        },
      };

    case 'UPDATE_LIKE':
      return {
        ...state,
        userState: {
          ...state.userState,
          isLiked: action.payload.isLiked,
        },
        stats: {
          ...state.stats,
          likes: action.payload.likeCount ?? state.stats.likes,
        },
      };

    case 'UPDATE_BOOKMARK':
      return {
        ...state,
        userState: {
          ...state.userState,
          isBookmarked: action.payload.isBookmarked,
        },
      };

    case 'UPDATE_STATS':
      return {
        ...state,
        stats: {
          ...state.stats,
          ...action.payload,
        },
      };

    case 'INCREMENT_SHARE_COUNT':
      return {
        ...state,
        stats: {
          ...state.stats,
          shares: state.stats.shares + 1,
        },
      };

    case 'INCREMENT_VIEW_COUNT':
      return {
        ...state,
        stats: {
          ...state.stats,
          views: state.stats.views + 1,
        },
      };

    case 'SET_COMMENTS':
      return {
        ...state,
        comments: {
          ...state.comments,
          list: action.payload.comments,
        },
      };

    case 'ADD_COMMENT':
      const newComment = action.payload.comment;
      const updatedComments = newComment.parentId
        ? addReplyToCommentTree(state.comments.list, newComment.parentId, newComment)
        : [...state.comments.list, newComment];
      
      return {
        ...state,
        comments: {
          ...state.comments,
          list: updatedComments,
        },
        stats: {
          ...state.stats,
          comments: state.stats.comments + 1,
        },
      };

    case 'UPDATE_COMMENT':
      return {
        ...state,
        comments: {
          ...state.comments,
          list: updateCommentInTree(state.comments.list, action.payload.commentId, action.payload.updates),
        },
      };

    case 'REMOVE_COMMENT':
      return {
        ...state,
        comments: {
          ...state.comments,
          list: removeCommentFromTree(state.comments.list, action.payload.commentId),
        },
        stats: {
          ...state.stats,
          comments: Math.max(state.stats.comments - 1, 0),
        },
      };

    case 'SET_COMMENT_LIKE':
      return {
        ...state,
        comments: {
          ...state.comments,
          list: updateCommentInTree(state.comments.list, action.payload.commentId, {
            isLiked: action.payload.isLiked,
            likeCount: action.payload.likeCount,
          }),
          likedComments: {
            ...state.comments.likedComments,
            [action.payload.commentId]: {
              isLiked: action.payload.isLiked,
              timestamp: Date.now(),
            },
          },
        },
      };

    case 'SET_LIKED_COMMENTS':
      return {
        ...state,
        comments: {
          ...state.comments,
          likedComments: action.payload.likedComments,
        },
      };

    case 'SET_NEW_COMMENT_ID':
      return {
        ...state,
        comments: {
          ...state.comments,
          newCommentId: action.payload.commentId,
        },
      };

    case 'SET_REPLYING_TO':
      return {
        ...state,
        comments: {
          ...state.comments,
          replyingTo: action.payload.commentId,
        },
      };

    case 'SET_COMMENTS_LOADING':
      return {
        ...state,
        comments: {
          ...state.comments,
          loading: action.payload.loading,
        },
      };

    case 'SET_REALTIME_STATE':
      return {
        ...state,
        comments: {
          ...state.comments,
          connectionState: action.payload.connectionState,
          onlineUserCount: action.payload.onlineUserCount ?? state.comments.onlineUserCount,
        },
      };

    case 'SET_TYPING_USERS':
      return {
        ...state,
        comments: {
          ...state.comments,
          typingUsers: action.payload.typingUsers,
        },
      };

    case 'ADD_REALTIME_COMMENT':
      // 检查评论是否已存在，避免重复
      const existingComment = findCommentInTree(state.comments.list, action.payload.comment.id);
      if (existingComment) {
        return state;
      }

      const realtimeComment = action.payload.comment;
      const updatedRealtimeComments = realtimeComment.parentId
        ? addReplyToCommentTree(state.comments.list, realtimeComment.parentId, realtimeComment)
        : [...state.comments.list, realtimeComment];
      
      return {
        ...state,
        comments: {
          ...state.comments,
          list: updatedRealtimeComments,
        },
        stats: {
          ...state.stats,
          comments: state.stats.comments + 1,
        },
      };

    default:
      return state;
  }
}

// Context 创建
const BlogInteractionsContext = createContext<BlogInteractionsContextType | undefined>(undefined);

// Provider 组件 Props
interface BlogInteractionsProviderProps {
  children: ReactNode;
  postId: string;
  userId?: string | undefined;
  initialData?: {
    stats: InteractionStats;
    userState: UserState;
    comments?: Comment[];
  } | null | undefined;
}

// Provider 组件
export function BlogInteractionsProvider({
  children,
  postId,
  userId,
  initialData,
}: BlogInteractionsProviderProps) {
  const initialState: BlogInteractionsState = {
    stats: initialData?.stats || {
      views: 0,
      likes: 0,
      shares: 0,
      comments: 0,
    },
    userState: initialData?.userState || {
      isLiked: false,
      isBookmarked: false,
    },
    comments: {
      list: initialData?.comments || [],
      likedComments: {},
      loading: false,
      submitting: false,
      newCommentId: null,
      replyingTo: null,
      // 实时状态
      realtimeEnabled: true,
      connectionState: 'disconnected',
      onlineUserCount: 0,
      typingUsers: [],
    },
    loading: {
      like: false,
      bookmark: false,
      commentSubmit: false,
      commentLike: false,
    },
  };

  const [state, dispatch] = useReducer(blogInteractionsReducer, initialState);

  // 初始化缓存和用户行为跟踪
  useEffect(() => {
    // 记录用户访问
    UserBehaviorAnalyzer.recordVisit();
    
    // 如果有初始评论数据，缓存它们
    if (initialData?.comments && initialData.comments.length > 0) {
      commentCache.setComments(postId, initialData.comments, userId, {
        ttl: 60 * 60 * 1000, // 1小时
        priority: 'high',
        enablePersistence: true,
      });
    }

    // 智能预加载相关文章评论
    const preloadRelatedComments = async () => {
      try {
        // 获取相关文章ID（这里简化实现，实际可以从API获取）
        const relatedPostIds = await getRelatedPostIds(postId);
        
        if (relatedPostIds.length > 0) {
          // 延迟预加载，避免影响当前页面性能
          setTimeout(() => {
            commentCache.preloadComments(relatedPostIds, userId);
          }, 2000);
        }
      } catch (error) {
        console.log('Preload failed:', error);
      }
    };

    preloadRelatedComments();

    // 初始化实时功能
    const initRealtime = async () => {
      try {
        await realtimeCommentManager.connectToPost(postId, userId);
        
        // 监听实时评论更新
        const unsubscribe = realtimeCommentManager.onCommentUpdate((comment) => {
          dispatch({ type: 'ADD_REALTIME_COMMENT', payload: { comment } });
        });

        return unsubscribe;
      } catch (error) {
        console.log('Realtime initialization failed:', error);
        return () => {}; // 空的取消函数
      }
    };

    initRealtime().then(unsubscribe => {
      // 存储取消函数以便清理
      return () => {
        unsubscribe();
        realtimeCommentManager.disconnect();
      };
    });

  }, [postId, userId, initialData?.comments]);

  // 获取相关文章ID（简化实现）
  const getRelatedPostIds = async (currentPostId: string): Promise<string[]> => {
    try {
      // 这里可以调用API获取相关文章
      // 暂时返回空数组，避免不必要的请求
      return [];
    } catch (error) {
      return [];
    }
  };

  // 获取客户端IP地址（用于游客操作）
  const getClientInfo = () => {
    return {
      userAgent: navigator.userAgent,
      timestamp: Date.now(),
    };
  };

  // 点赞处理
  const handleLike = async () => {
    if (state.loading.like) return;

    dispatch({ type: 'SET_LOADING', payload: { action: 'like', loading: true } });

    // 乐观更新
    const newIsLiked = !state.userState.isLiked;
    const newLikeCount = newIsLiked 
      ? state.stats.likes + 1 
      : Math.max(state.stats.likes - 1, 0);

    dispatch({
      type: 'UPDATE_LIKE',
      payload: { isLiked: newIsLiked, likeCount: newLikeCount },
    });

    try {
      const response = await fetch(`/api/blog/${postId}/interactions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: newIsLiked ? 'like' : 'unlike',
          userId,
          ...getClientInfo(),
        }),
      });

      const data = await response.json();

      if (!data.success) {
        // 回滚乐观更新
        dispatch({
          type: 'UPDATE_LIKE',
          payload: { 
            isLiked: !newIsLiked, 
            likeCount: newIsLiked ? newLikeCount - 1 : newLikeCount + 1 
          },
        });
        throw new Error(data.error || 'Failed to update like');
      }

      // 更新本地存储（降级兼容）
      const likedPosts = JSON.parse(localStorage.getItem('likedPosts') || '[]');
      if (newIsLiked) {
        if (!likedPosts.includes(postId)) {
          likedPosts.push(postId);
        }
      } else {
        const index = likedPosts.indexOf(postId);
        if (index > -1) {
          likedPosts.splice(index, 1);
        }
      }
      localStorage.setItem('likedPosts', JSON.stringify(likedPosts));

    } catch (error) {
      console.error('Failed to update like:', error);
      // 错误处理已在上面完成
    } finally {
      dispatch({ type: 'SET_LOADING', payload: { action: 'like', loading: false } });
    }
  };

  // 收藏处理
  const handleBookmark = async () => {
    if (state.loading.bookmark) return;

    if (!userId) {
      alert('请先登录后再收藏文章');
      return;
    }

    dispatch({ type: 'SET_LOADING', payload: { action: 'bookmark', loading: true } });

    // 乐观更新
    const newIsBookmarked = !state.userState.isBookmarked;
    dispatch({
      type: 'UPDATE_BOOKMARK',
      payload: { isBookmarked: newIsBookmarked },
    });

    try {
      const response = await fetch(`/api/blog/${postId}/interactions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: newIsBookmarked ? 'bookmark' : 'unbookmark',
          userId,
          ...getClientInfo(),
        }),
      });

      const data = await response.json();

      if (!data.success) {
        // 回滚乐观更新
        dispatch({
          type: 'UPDATE_BOOKMARK',
          payload: { isBookmarked: !newIsBookmarked },
        });
        throw new Error(data.error || 'Failed to update bookmark');
      }

      // 更新本地存储（降级兼容）
      const bookmarkedPosts = JSON.parse(localStorage.getItem('bookmarkedPosts') || '[]');
      if (newIsBookmarked) {
        if (!bookmarkedPosts.includes(postId)) {
          bookmarkedPosts.push(postId);
        }
      } else {
        const index = bookmarkedPosts.indexOf(postId);
        if (index > -1) {
          bookmarkedPosts.splice(index, 1);
        }
      }
      localStorage.setItem('bookmarkedPosts', JSON.stringify(bookmarkedPosts));

    } catch (error) {
      console.error('Failed to update bookmark:', error);
    } finally {
      dispatch({ type: 'SET_LOADING', payload: { action: 'bookmark', loading: false } });
    }
  };

  // 分享处理
  const handleShare = async (platform: string = 'general') => {
    // 乐观更新分享计数
    dispatch({ type: 'INCREMENT_SHARE_COUNT' });

    try {
      const response = await fetch(`/api/blog/${postId}/interactions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'share',
          userId,
          platform,
          ...getClientInfo(),
        }),
      });

      const data = await response.json();
      
      if (!data.success) {
        console.warn('Failed to record share:', data.error);
        // 不回滚UI，分享记录失败不影响用户体验
      }

    } catch (error) {
      console.error('Failed to record share:', error);
      // 分享记录失败不影响用户体验，不回滚UI
    }
  };

  // 更新统计数据
  const updateStats = (stats: Partial<InteractionStats>) => {
    dispatch({ type: 'UPDATE_STATS', payload: stats });
  };

  // 评论相关actions
  const setComments = (comments: Comment[]) => {
    dispatch({ type: 'SET_COMMENTS', payload: { comments } });
  };

  const addComment = (comment: Comment) => {
    dispatch({ type: 'ADD_COMMENT', payload: { comment } });
  };

  const updateComment = (commentId: string, updates: Partial<Comment>) => {
    dispatch({ type: 'UPDATE_COMMENT', payload: { commentId, updates } });
  };

  const removeComment = (commentId: string) => {
    dispatch({ type: 'REMOVE_COMMENT', payload: { commentId } });
  };

  const setReplyingTo = (commentId: string | null) => {
    dispatch({ type: 'SET_REPLYING_TO', payload: { commentId } });
  };

  // 评论点赞处理
  const handleCommentLike = async (commentId: string) => {
    const currentComment = findCommentInTree(state.comments.list, commentId);
    if (!currentComment) return;

    dispatch({ type: 'SET_LOADING', payload: { action: 'commentLike', loading: true } });

    // 乐观更新
    const newIsLiked = !currentComment.isLiked;
    const newLikeCount = newIsLiked 
      ? (currentComment.likeCount || 0) + 1 
      : Math.max((currentComment.likeCount || 0) - 1, 0);

    dispatch({
      type: 'SET_COMMENT_LIKE',
      payload: { commentId, isLiked: newIsLiked, likeCount: newLikeCount },
    });

    try {
      const response = await fetch(`/api/blog/${postId}/comments/${commentId}/like`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId }),
      });

      const data = await response.json();

      if (data.success) {
        // 用服务器返回的准确数据更新
        dispatch({
          type: 'SET_COMMENT_LIKE',
          payload: { 
            commentId, 
            isLiked: data.data.isLiked, 
            likeCount: data.data.likeCount 
          },
        });
      } else {
        // 回滚状态
        dispatch({
          type: 'SET_COMMENT_LIKE',
          payload: { 
            commentId, 
            isLiked: currentComment.isLiked || false, 
            likeCount: currentComment.likeCount || 0 
          },
        });
        throw new Error(data.error || '点赞失败');
      }
    } catch (error) {
      console.error('Failed to like comment:', error);
      // 回滚状态
      dispatch({
        type: 'SET_COMMENT_LIKE',
        payload: { 
          commentId, 
          isLiked: currentComment.isLiked || false, 
          likeCount: currentComment.likeCount || 0 
        },
      });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: { action: 'commentLike', loading: false } });
    }
  };

  // 评论提交处理
  const submitComment = async (
    content: string, 
    parentId?: string, 
    guestName?: string, 
    guestEmail?: string
  ): Promise<Comment> => {
    if (!content.trim()) {
      throw new Error('评论内容不能为空');
    }

    if (!userId && (!guestName?.trim() || !guestEmail?.trim())) {
      throw new Error('请填写姓名和邮箱');
    }

    dispatch({ type: 'SET_LOADING', payload: { action: 'commentSubmit', loading: true } });

    // 创建乐观评论
    const optimisticComment: Comment = {
      id: `temp_${Date.now()}`,
      content: content.trim(),
      userId: userId || undefined,
      userName: userId ? '当前用户' : (guestName || '匿名用户'),
      userAvatar: undefined,
      postId: postId,
      parentId: parentId || undefined,
      isApproved: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      guestName: !userId ? guestName : undefined,
      guestEmail: !userId ? guestEmail : undefined,
      likeCount: 0,
      isLiked: false,
      isOptimistic: true,
    };

    // 乐观更新
    dispatch({ type: 'ADD_COMMENT', payload: { comment: optimisticComment } });

    try {
      const response = await fetch(`/api/blog/${postId}/comments`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: content.trim(),
          parentId,
          userId,
          guestName: !userId ? guestName : undefined,
          guestEmail: !userId ? guestEmail : undefined,
        }),
      });

      const data = await response.json();

      if (data.success) {
        // 用真实数据替换乐观评论
        dispatch({ type: 'REMOVE_COMMENT', payload: { commentId: optimisticComment.id } });
        dispatch({ type: 'ADD_COMMENT', payload: { comment: data.data } });
        
        // 设置新评论ID用于高亮
        dispatch({ type: 'SET_NEW_COMMENT_ID', payload: { commentId: data.data.id } });
        
        // 3秒后移除高亮
        setTimeout(() => {
          dispatch({ type: 'SET_NEW_COMMENT_ID', payload: { commentId: null } });
        }, 3000);

        // 记录用户行为
        UserBehaviorAnalyzer.recordComment();
        
        // 使缓存失效并更新
        await commentCache.invalidateComments(postId, userId);
        const updatedComments = [...state.comments.list.filter(c => c.id !== optimisticComment.id), data.data];
        await commentCache.setComments(postId, updatedComments, userId, {
          ttl: 60 * 60 * 1000, // 1小时
          priority: 'high',
          enablePersistence: true,
        });

        return data.data;
      } else {
        // 移除乐观评论
        dispatch({ type: 'REMOVE_COMMENT', payload: { commentId: optimisticComment.id } });
        throw new Error(data.error || '评论发表失败');
      }
    } catch (error) {
      // 移除乐观评论
      dispatch({ type: 'REMOVE_COMMENT', payload: { commentId: optimisticComment.id } });
      throw error;
    } finally {
      dispatch({ type: 'SET_LOADING', payload: { action: 'commentSubmit', loading: false } });
    }
  };

  // 智能加载评论（缓存优先）
  const loadComments = async () => {
    dispatch({ type: 'SET_COMMENTS_LOADING', payload: { loading: true } });

    try {
      // 首先尝试从缓存获取
      const cachedComments = await commentCache.getComments(postId, userId);
      
      if (cachedComments) {
        dispatch({ type: 'SET_COMMENTS', payload: { comments: cachedComments } });
        console.log('Comments loaded from cache');
        
        // 在后台刷新缓存（可选）
        setTimeout(() => {
          refreshCommentsInBackground();
        }, 1000);
        
        return;
      }

      // 缓存未命中，从网络获取
      console.log('Loading comments from network');
      const params = new URLSearchParams();
      if (userId) params.set('userId', userId);

      const response = await fetch(`/api/blog/${postId}/comments?${params}`);
      const data = await response.json();

      if (data.success) {
        const comments = data.data;
        dispatch({ type: 'SET_COMMENTS', payload: { comments } });
        
        // 缓存评论数据
        const userBehavior = UserBehaviorAnalyzer.analyze();
        const cacheConfig = {
          ttl: userBehavior.isFrequentVisitor ? 24 * 60 * 60 * 1000 : 10 * 60 * 1000, // 常客24小时，普通用户10分钟
          priority: userBehavior.isActiveCommenter ? 'high' : 'medium' as const,
          enablePersistence: true,
        };
        
        await commentCache.setComments(postId, comments, userId, cacheConfig);
        console.log('Comments cached with config:', cacheConfig);
      } else {
        throw new Error(data.error || '加载评论失败');
      }
    } catch (error) {
      console.error('Failed to load comments:', error);
      throw error;
    } finally {
      dispatch({ type: 'SET_COMMENTS_LOADING', payload: { loading: false } });
    }
  };

  // 后台刷新评论缓存
  const refreshCommentsInBackground = async () => {
    try {
      const params = new URLSearchParams();
      if (userId) params.set('userId', userId);

      const response = await fetch(`/api/blog/${postId}/comments?${params}`);
      const data = await response.json();

      if (data.success) {
        // 更新缓存
        await commentCache.setComments(postId, data.data, userId);
        
        // 如果内容有变化，静默更新state
        if (JSON.stringify(data.data) !== JSON.stringify(state.comments.list)) {
          dispatch({ type: 'SET_COMMENTS', payload: { comments: data.data } });
          console.log('Comments refreshed in background');
        }
      }
    } catch (error) {
      console.log('Background refresh failed:', error);
      // 静默失败，不影响用户体验
    }
  };

  // 实时功能actions
  const sendTypingIndicator = (isTyping: boolean) => {
    realtimeCommentManager.sendTypingIndicator(isTyping);
  };

  const connectRealtime = async () => {
    try {
      await realtimeCommentManager.connectToPost(postId, userId);
      dispatch({ 
        type: 'SET_REALTIME_STATE', 
        payload: { connectionState: 'connected' } 
      });
    } catch (error) {
      dispatch({ 
        type: 'SET_REALTIME_STATE', 
        payload: { connectionState: 'error' } 
      });
      throw error;
    }
  };

  const disconnectRealtime = () => {
    realtimeCommentManager.disconnect();
    dispatch({ 
      type: 'SET_REALTIME_STATE', 
      payload: { connectionState: 'disconnected' } 
    });
  };

  const contextValue: BlogInteractionsContextType = {
    state,
    actions: {
      handleLike,
      handleBookmark,
      handleShare,
      updateStats,
      // 评论actions
      setComments,
      addComment,
      updateComment,
      removeComment,
      handleCommentLike,
      submitComment,
      setReplyingTo,
      loadComments,
      // 实时功能
      sendTypingIndicator,
      connectRealtime,
      disconnectRealtime,
    },
  };

  return (
    <BlogInteractionsContext.Provider value={contextValue}>
      {children}
    </BlogInteractionsContext.Provider>
  );
}

// Hook 用于访问 Context
export function useBlogInteractions() {
  const context = useContext(BlogInteractionsContext);
  if (context === undefined) {
    throw new Error('useBlogInteractions must be used within a BlogInteractionsProvider');
  }
  return context;
}

// 辅助 Hooks
export function useBlogStats() {
  const { state } = useBlogInteractions();
  return state.stats;
}

export function useBlogUserState() {
  const { state } = useBlogInteractions();
  return state.userState;
}

export function useBlogActions() {
  const { actions } = useBlogInteractions();
  return actions;
}

// 评论相关hooks
export function useBlogComments() {
  const { state } = useBlogInteractions();
  return state.comments;
}

export function useCommentActions() {
  const { actions } = useBlogInteractions();
  return {
    setComments: actions.setComments,
    addComment: actions.addComment,
    updateComment: actions.updateComment,
    removeComment: actions.removeComment,
    handleCommentLike: actions.handleCommentLike,
    submitComment: actions.submitComment,
    setReplyingTo: actions.setReplyingTo,
    loadComments: actions.loadComments,
  };
}

// 实时功能hooks
export function useRealtimeState() {
  const { state } = useBlogInteractions();
  return {
    connectionState: state.comments.connectionState,
    onlineUserCount: state.comments.onlineUserCount,
    typingUsers: state.comments.typingUsers,
    realtimeEnabled: state.comments.realtimeEnabled,
  };
}

export function useRealtimeActions() {
  const { actions } = useBlogInteractions();
  return {
    sendTypingIndicator: actions.sendTypingIndicator,
    connectRealtime: actions.connectRealtime,
    disconnectRealtime: actions.disconnectRealtime,
  };
}

// 组合hooks for convenience
export function useCommentsWithActions() {
  const comments = useBlogComments();
  const actions = useCommentActions();
  const realtimeActions = useRealtimeActions();
  const realtimeState = useRealtimeState();
  const { state } = useBlogInteractions();
  
  return {
    comments: comments.list,
    likedComments: comments.likedComments,
    loading: comments.loading,
    submitting: state.loading.commentSubmit,
    newCommentId: comments.newCommentId,
    replyingTo: comments.replyingTo,
    // 实时状态
    connectionState: realtimeState.connectionState,
    onlineUserCount: realtimeState.onlineUserCount,
    typingUsers: realtimeState.typingUsers,
    realtimeEnabled: realtimeState.realtimeEnabled,
    ...actions,
    ...realtimeActions,
  };
}