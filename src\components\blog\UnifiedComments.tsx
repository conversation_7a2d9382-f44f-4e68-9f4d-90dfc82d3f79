'use client';

import React, { useEffect } from 'react';
import { MessageCircle, User, ThumbsUp, Reply, Flag, Send } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useCommentsWithActions } from '@/components/providers/blog-interactions-provider';
import { showToast } from '@/components/ui/toast';
import { useSmoothCommentUpdater, SmoothAnimationStyles } from './SmoothCommentUpdater';
import { RealtimeIndicators, RealtimeBadge } from './RealtimeIndicators';

interface UnifiedCommentsProps {
  postId: string;
  userId?: string;
  allowGuests?: boolean;
  className?: string;
}

/**
 * 统一的评论组件 - 使用新的Context状态管理
 * 结合了评论显示、交互和提交功能
 */
export function UnifiedComments({
  postId,
  userId,
  allowGuests = true,
  className,
}: UnifiedCommentsProps) {
  const {
    comments,
    likedComments,
    loading,
    submitting,
    newCommentId,
    replyingTo,
    handleCommentLike,
    submitComment,
    setReplyingTo,
    loadComments,
    // 实时状态和功能
    connectionState,
    onlineUserCount,
    typingUsers,
    realtimeEnabled,
    sendTypingIndicator,
  } = useCommentsWithActions();

  const {
    playSubmitAnimation,
    playSuccessAnimation,
    playErrorAnimation,
    smartScrollToNewComment,
    preserveUserContext,
    restoreUserContext,
  } = useSmoothCommentUpdater();

  const [newComment, setNewComment] = React.useState('');
  const [guestName, setGuestName] = React.useState('');
  const [guestEmail, setGuestEmail] = React.useState('');

  // 初始化本地存储的点赞状态
  useEffect(() => {
    try {
      const stored = localStorage.getItem('likedComments');
      if (stored) {
        const parsed = JSON.parse(stored);
        // 这里可以同步本地存储到Context中
      }
    } catch (error) {
      console.error('Failed to load liked comments from localStorage:', error);
    }
  }, []);

  // 同步状态到本地存储
  useEffect(() => {
    if (Object.keys(likedComments).length > 0) {
      try {
        localStorage.setItem('likedComments', JSON.stringify(likedComments));
      } catch (error) {
        console.error('Failed to save liked comments to localStorage:', error);
      }
    }
  }, [likedComments]);

  const formatDate = (dateInput: Date | string) => {
    const date = dateInput instanceof Date ? dateInput : new Date(dateInput);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}小时前`;
    } else if (diffInHours < 24 * 7) {
      return `${Math.floor(diffInHours / 24)}天前`;
    } else {
      return date.toLocaleDateString();
    }
  };

  const handleSubmitComment = async (content: string, parentId?: string) => {
    // 保存用户上下文
    const userContext = preserveUserContext();
    
    // 创建临时评论ID用于动画
    const tempCommentId = `temp_${Date.now()}`;
    let tempElement: HTMLElement | null = null;
    
    try {
      // 创建乐观更新的临时评论元素（用于动画）
      tempElement = document.createElement('div');
      tempElement.setAttribute('data-comment-id', tempCommentId);
      tempElement.className = 'comment-optimistic opacity-70';
      tempElement.innerHTML = `
        <div class="p-4 bg-blue-50 dark:bg-blue-900/10 rounded-lg border border-blue-200 dark:border-blue-800">
          <div class="flex items-center gap-2 mb-2">
            <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center">
              <svg class="w-4 h-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
              </svg>
            </div>
            <span class="font-medium text-gray-900 dark:text-gray-100">
              ${userId ? '当前用户' : (guestName || '匿名用户')}
            </span>
            <span class="text-sm text-gray-500">发送中...</span>
          </div>
          <p class="text-gray-700 dark:text-gray-300 ml-10">${content}</p>
        </div>
      `;

      // 将临时元素添加到适当位置
      const commentsContainer = document.querySelector('.comments-list') || 
                               document.querySelector('#comments .space-y-6');
      if (commentsContainer) {
        if (parentId) {
          // 如果是回复，添加到父评论下
          const parentElement = document.querySelector(`[data-comment-id="${parentId}"]`);
          const repliesContainer = parentElement?.querySelector('.replies-container') || 
                                 parentElement?.parentElement;
          if (repliesContainer) {
            repliesContainer.appendChild(tempElement);
          }
        } else {
          // 如果是主评论，添加到评论列表末尾
          commentsContainer.appendChild(tempElement);
        }
      }

      // 播放提交动画
      playSubmitAnimation(tempCommentId);

      // 提交评论
      const result = await submitComment(content, parentId, guestName, guestEmail);
      
      // 移除临时元素
      if (tempElement) {
        tempElement.remove();
      }
      
      // 播放成功动画
      if (result && result.id) {
        setTimeout(() => {
          playSuccessAnimation(result.id);
          smartScrollToNewComment(result.id);
        }, 100);
      }
      
      // 清空表单
      setNewComment('');
      if (!userId) {
        setGuestName('');
        setGuestEmail('');
      }
      
      // 关闭回复表单
      if (parentId) {
        setReplyingTo(null);
      }

      showToast({
        message: '评论发表成功！',
        type: 'success',
        duration: 3000,
        position: 'top-center'
      });

    } catch (error) {
      // 移除临时元素
      if (tempElement) {
        tempElement.remove();
      }
      
      // 播放错误动画
      playErrorAnimation(tempCommentId);
      
      // 恢复用户上下文
      restoreUserContext(userContext, true);

      showToast({
        message: error instanceof Error ? error.message : '评论发表失败，请重试',
        type: 'error',
        duration: 3000,
        position: 'top-center'
      });
    }
  };

  const handleCommentLikeClick = async (commentId: string) => {
    try {
      await handleCommentLike(commentId);
    } catch (error) {
      showToast({
        message: '操作失败，请重试',
        type: 'error',
        duration: 3000,
        position: 'top-center'
      });
    }
  };

  const renderComment = (comment: any, isReply = false) => {
    const isNewComment = newCommentId === comment.id;
    const isLiked = likedComments[comment.id]?.isLiked || comment.isLiked;
    const displayName = comment.userName || comment.guestName || '匿名用户';
    
    return (
      <article
        key={comment.id}
        data-comment-id={comment.id}
        className={cn(
          'comment-item border-b border-gray-100 dark:border-gray-800 pb-6 mb-6 transition-all duration-500',
          isReply && 'ml-8 border-l-2 border-gray-200 dark:border-gray-700 pl-4',
          isNewComment && 'comment-highlight bg-blue-50 dark:bg-blue-900/10 border-blue-200 dark:border-blue-800 rounded-lg p-4 -m-4 mb-0',
          comment.isOptimistic && 'comment-optimistic opacity-70 animate-pulse',
          submitting && 'comment-submitting pointer-events-none'
        )}
      >
        <header className="flex items-start gap-3 mb-3">
          {/* 用户头像 */}
          <div className="flex-shrink-0">
            {comment.userAvatar ? (
              <img
                src={comment.userAvatar}
                alt={displayName}
                className="w-10 h-10 rounded-full object-cover"
              />
            ) : (
              <div className={cn(
                "w-10 h-10 rounded-full flex items-center justify-center",
                comment.userId 
                  ? "bg-blue-100 dark:bg-blue-900/20" 
                  : "bg-gray-300 dark:bg-gray-600"
              )}>
                <User className={cn(
                  "w-5 h-5",
                  comment.userId 
                    ? "text-blue-600 dark:text-blue-400" 
                    : "text-gray-600 dark:text-gray-400"
                )} />
              </div>
            )}
          </div>

          {/* 评论信息 */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <span className="font-medium text-gray-900 dark:text-gray-100">
                {displayName}
              </span>
              {!comment.userId && (
                <span className="text-xs px-2 py-0.5 bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-400 rounded">
                  游客
                </span>
              )}
              <span className="text-sm text-gray-500 dark:text-gray-400">
                {formatDate(comment.createdAt)}
              </span>
            </div>
          </div>
        </header>

        {/* 评论内容 */}
        <div className="ml-13">
          <div className="prose prose-sm max-w-none text-gray-700 dark:text-gray-300 mb-3">
            <p className="whitespace-pre-wrap break-words leading-relaxed">
              {comment.content}
            </p>
          </div>

          {/* 评论操作 */}
          <div className="flex items-center gap-4 text-sm">
            <button
              onClick={() => handleCommentLikeClick(comment.id)}
              disabled={comment.isOptimistic}
              className={cn(
                'flex items-center gap-1 transition-colors',
                isLiked
                  ? 'text-red-500'
                  : 'text-gray-500 dark:text-gray-400 hover:text-red-500',
                comment.isOptimistic && 'opacity-50 cursor-not-allowed'
              )}
            >
              <ThumbsUp className={cn('w-4 h-4', isLiked && 'fill-current')} />
              <span>{comment.likeCount || 0}</span>
            </button>

            <button
              onClick={() => setReplyingTo(replyingTo === comment.id ? null : comment.id)}
              disabled={comment.isOptimistic}
              className={cn(
                'text-gray-500 dark:text-gray-400 hover:text-blue-500 transition-colors',
                comment.isOptimistic && 'opacity-50 cursor-not-allowed'
              )}
            >
              <Reply className="w-4 h-4 inline mr-1" />
              回复
            </button>

            <button className="text-gray-500 dark:text-gray-400 hover:text-orange-500 transition-colors">
              <Flag className="w-4 h-4 inline mr-1" />
              举报
            </button>
          </div>

          {/* 回复表单 */}
          {replyingTo === comment.id && (
            <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <ReplyForm
                comment={comment}
                onSubmit={(content) => handleSubmitComment(content, comment.id)}
                onCancel={() => setReplyingTo(null)}
                userId={userId}
                allowGuests={allowGuests}
                submitting={submitting}
              />
            </div>
          )}

          {/* 渲染回复 */}
          {comment.replies && comment.replies.length > 0 && (
            <div className="mt-6 replies-container">
              {comment.replies.map((reply: any) => renderComment(reply, true))}
            </div>
          )}
        </div>
      </article>
    );
  };

  if (loading && comments.length === 0) {
    return (
      <div id="comments" className={cn('py-8', className)}>
        <div className="flex items-center justify-center">
          <div className="w-6 h-6 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin" />
          <span className="ml-2 text-gray-600 dark:text-gray-400">加载评论中...</span>
        </div>
      </div>
    );
  }

  return (
    <div id="comments" className={cn('py-8', className)}>
      {/* 评论标题 */}
      <div className="flex items-center justify-between gap-3 mb-6">
        <div className="flex items-center gap-3">
          <MessageCircle className="w-6 h-6 text-gray-600 dark:text-gray-400" />
          <h3 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            评论 ({comments.length})
          </h3>
          <RealtimeBadge show={realtimeEnabled && connectionState === 'connected'} />
        </div>
        
        {/* 实时状态指示器 */}
        {realtimeEnabled && (
          <RealtimeIndicators
            connectionState={connectionState}
            onlineCount={onlineUserCount}
            typingUsers={typingUsers}
            className="hidden md:flex"
          />
        )}
      </div>

      {/* 发表评论表单 */}
      <div className="mb-8 p-6 bg-gray-50 dark:bg-gray-800 rounded-xl">
        <h4 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
          发表评论
        </h4>
        
        <CommentForm
          onSubmit={(content) => handleSubmitComment(content)}
          userId={userId}
          allowGuests={allowGuests}
          submitting={submitting}
          guestName={guestName}
          setGuestName={setGuestName}
          guestEmail={guestEmail}
          setGuestEmail={setGuestEmail}
          newComment={newComment}
          setNewComment={setNewComment}
          sendTypingIndicator={sendTypingIndicator}
        />
      </div>

      {/* 评论列表 */}
      <div className="space-y-6 comments-list">
        {comments.length === 0 ? (
          <div className="text-center py-12">
            <MessageCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500 dark:text-gray-400 text-lg">
              还没有评论，来发表第一个评论吧！
            </p>
          </div>
        ) : (
          comments.map(comment => renderComment(comment))
        )}
      </div>

      {/* 添加动画样式 */}
      <SmoothAnimationStyles />
    </div>
  );
}

// 评论表单子组件
interface CommentFormProps {
  onSubmit: (content: string) => void;
  userId?: string;
  allowGuests: boolean;
  submitting: boolean;
  guestName: string;
  setGuestName: (name: string) => void;
  guestEmail: string;
  setGuestEmail: (email: string) => void;
  newComment: string;
  setNewComment: (comment: string) => void;
  sendTypingIndicator?: (isTyping: boolean) => void;
}

function CommentForm({
  onSubmit,
  userId,
  allowGuests,
  submitting,
  guestName,
  setGuestName,
  guestEmail,
  setGuestEmail,
  newComment,
  setNewComment,
  sendTypingIndicator,
}: CommentFormProps) {
  // 打字指示器防抖
  const typingTimeoutRef = React.useRef<NodeJS.Timeout | null>(null);

  const handleContentChange = (value: string) => {
    setNewComment(value);
    
    // 发送打字指示器
    if (sendTypingIndicator && userId) {
      sendTypingIndicator(true);
      
      // 清除之前的定时器
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
      
      // 3秒后停止打字指示器
      typingTimeoutRef.current = setTimeout(() => {
        sendTypingIndicator(false);
      }, 3000);
    }
  };

  // 清理
  React.useEffect(() => {
    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
      if (sendTypingIndicator && userId) {
        sendTypingIndicator(false);
      }
    };
  }, [sendTypingIndicator, userId]);
  const handleSubmit = () => {
    if (!newComment.trim()) return;
    if (!userId && (!guestName.trim() || !guestEmail.trim())) return;
    
    onSubmit(newComment);
  };

  return (
    <>
      {!userId && allowGuests && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <input
            type="text"
            placeholder="您的姓名 *"
            value={guestName}
            onChange={(e) => setGuestName(e.target.value)}
            className="px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg
              bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100
              focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
          <input
            type="email"
            placeholder="您的邮箱 *"
            value={guestEmail}
            onChange={(e) => setGuestEmail(e.target.value)}
            className="px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg
              bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100
              focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
      )}
      
      <textarea
        value={newComment}
        onChange={(e) => handleContentChange(e.target.value)}
        placeholder="写下您的评论..."
        className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg
          bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100
          focus:ring-2 focus:ring-blue-500 focus:border-transparent
          resize-none"
        rows={4}
      />
      
      <div className="flex justify-end items-center mt-4">
        <button
          onClick={handleSubmit}
          disabled={submitting || !newComment.trim() || (!userId && (!guestName.trim() || !guestEmail.trim()))}
          className="px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600
            disabled:opacity-50 disabled:cursor-not-allowed
            flex items-center gap-2 transition-colors"
        >
          {submitting ? (
            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
          ) : (
            <Send className="w-4 h-4" />
          )}
          发表评论
        </button>
      </div>
    </>
  );
}

// 回复表单子组件
interface ReplyFormProps {
  comment: any;
  onSubmit: (content: string) => void;
  onCancel: () => void;
  userId?: string;
  allowGuests: boolean;
  submitting: boolean;
}

function ReplyForm({ comment, onSubmit, onCancel, userId, allowGuests, submitting }: ReplyFormProps) {
  const [replyContent, setReplyContent] = React.useState('');
  const [guestName, setGuestName] = React.useState('');
  const [guestEmail, setGuestEmail] = React.useState('');

  const handleSubmit = () => {
    if (!replyContent.trim()) return;
    if (!userId && (!guestName.trim() || !guestEmail.trim())) return;
    
    onSubmit(replyContent);
  };

  return (
    <>
      {!userId && allowGuests && (
        <div className="grid grid-cols-2 gap-3 mb-3">
          <input
            type="text"
            placeholder="您的姓名"
            value={guestName}
            onChange={(e) => setGuestName(e.target.value)}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg
              bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100
              focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
          <input
            type="email"
            placeholder="您的邮箱"
            value={guestEmail}
            onChange={(e) => setGuestEmail(e.target.value)}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg
              bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100
              focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
      )}
      
      <textarea
        value={replyContent}
        onChange={(e) => setReplyContent(e.target.value)}
        placeholder={`回复 ${comment.userName || comment.guestName || '匿名用户'}...`}
        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg
          bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100
          focus:ring-2 focus:ring-blue-500 focus:border-transparent
          resize-none"
        rows={3}
      />
      
      <div className="flex justify-end gap-2 mt-3">
        <button
          onClick={onCancel}
          className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"
        >
          取消
        </button>
        <button
          onClick={handleSubmit}
          disabled={submitting || !replyContent.trim() || (!userId && (!guestName.trim() || !guestEmail.trim()))}
          className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600
            disabled:opacity-50 disabled:cursor-not-allowed
            flex items-center gap-2"
        >
          {submitting ? (
            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
          ) : (
            <Send className="w-4 h-4" />
          )}
          发送回复
        </button>
      </div>
    </>
  );
}