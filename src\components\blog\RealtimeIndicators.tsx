'use client';

import React from 'react';
import { cn } from '@/lib/utils';
import { Wifi, WifiOff, Users, Edit3 } from 'lucide-react';

interface RealtimeIndicatorsProps {
  connectionState: string;
  onlineCount: number;
  typingUsers: Array<{
    userId: string;
    userName: string;
    isTyping: boolean;
    timestamp: number;
  }>;
  className?: string;
}

/**
 * 实时状态指示器组件
 * 显示连接状态、在线用户数、打字用户等信息
 */
export function RealtimeIndicators({
  connectionState,
  onlineCount,
  typingUsers,
  className,
}: RealtimeIndicatorsProps) {
  // 过滤出正在打字的用户
  const currentlyTyping = typingUsers.filter(user => {
    const timeSinceLastType = Date.now() - user.timestamp;
    return user.isTyping && timeSinceLastType < 10000; // 10秒内的打字状态
  });

  const getConnectionIcon = () => {
    switch (connectionState) {
      case 'connected':
        return <Wifi className="w-4 h-4 text-green-500" />;
      case 'connecting':
        return <Wifi className="w-4 h-4 text-yellow-500 animate-pulse" />;
      case 'disconnected':
      case 'error':
        return <WifiOff className="w-4 h-4 text-red-500" />;
      default:
        return <WifiOff className="w-4 h-4 text-gray-400" />;
    }
  };

  const getConnectionText = () => {
    switch (connectionState) {
      case 'connected':
        return '实时连接';
      case 'connecting':
        return '连接中...';
      case 'disconnected':
        return '已断线';
      case 'error':
        return '连接错误';
      default:
        return '未连接';
    }
  };

  const getConnectionColor = () => {
    switch (connectionState) {
      case 'connected':
        return 'text-green-600 dark:text-green-400';
      case 'connecting':
        return 'text-yellow-600 dark:text-yellow-400';
      case 'disconnected':
      case 'error':
        return 'text-red-600 dark:text-red-400';
      default:
        return 'text-gray-600 dark:text-gray-400';
    }
  };

  return (
    <div className={cn('flex items-center gap-4 text-sm', className)}>
      {/* 连接状态 */}
      <div className={cn('flex items-center gap-1.5', getConnectionColor())}>
        {getConnectionIcon()}
        <span className="hidden sm:inline">{getConnectionText()}</span>
      </div>

      {/* 在线用户数 */}
      {connectionState === 'connected' && onlineCount > 0 && (
        <div className="flex items-center gap-1.5 text-gray-600 dark:text-gray-400">
          <Users className="w-4 h-4" />
          <span>{onlineCount} 在线</span>
        </div>
      )}

      {/* 打字指示器 */}
      {currentlyTyping.length > 0 && (
        <div className="flex items-center gap-1.5 text-blue-600 dark:text-blue-400">
          <Edit3 className="w-4 h-4 animate-pulse" />
          <span>
            {currentlyTyping.length === 1
              ? `${currentlyTyping[0].userName} 正在输入...`
              : `${currentlyTyping.length} 人正在输入...`}
          </span>
        </div>
      )}
    </div>
  );
}

/**
 * 悬浮实时状态面板
 */
export function FloatingRealtimePanel({
  connectionState,
  onlineCount,
  typingUsers,
}: RealtimeIndicatorsProps) {
  const [isExpanded, setIsExpanded] = React.useState(false);

  // 只在有活动时显示
  const hasActivity = connectionState === 'connected' || typingUsers.length > 0;

  if (!hasActivity) return null;

  return (
    <div className="fixed bottom-4 left-4 z-40">
      <div
        className={cn(
          'bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 transition-all duration-300',
          isExpanded ? 'p-4' : 'p-2'
        )}
      >
        {/* 折叠状态 - 只显示图标 */}
        {!isExpanded && (
          <button
            onClick={() => setIsExpanded(true)}
            className="flex items-center gap-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"
          >
            {connectionState === 'connected' ? (
              <Wifi className="w-4 h-4 text-green-500" />
            ) : (
              <WifiOff className="w-4 h-4 text-red-500" />
            )}
            {onlineCount > 0 && (
              <span className="text-xs bg-blue-100 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 px-1.5 py-0.5 rounded-full">
                {onlineCount}
              </span>
            )}
          </button>
        )}

        {/* 展开状态 - 显示详细信息 */}
        {isExpanded && (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h3 className="font-medium text-gray-900 dark:text-gray-100">
                实时状态
              </h3>
              <button
                onClick={() => setIsExpanded(false)}
                className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
              >
                ×
              </button>
            </div>

            <RealtimeIndicators
              connectionState={connectionState}
              onlineCount={onlineCount}
              typingUsers={typingUsers}
              className="flex-col items-start gap-2"
            />

            {/* 在线用户详情 */}
            {connectionState === 'connected' && onlineCount > 1 && (
              <div className="pt-2 border-t border-gray-200 dark:border-gray-700">
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  有 {onlineCount} 人正在查看此文章
                </p>
              </div>
            )}

            {/* 打字用户列表 */}
            {typingUsers.length > 0 && (
              <div className="pt-2 border-t border-gray-200 dark:border-gray-700">
                <div className="space-y-1">
                  {typingUsers.slice(0, 3).map((user) => (
                    <div
                      key={user.userId}
                      className="flex items-center gap-2 text-xs text-blue-600 dark:text-blue-400"
                    >
                      <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
                      <span>{user.userName} 正在输入...</span>
                    </div>
                  ))}
                  {typingUsers.length > 3 && (
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      还有 {typingUsers.length - 3} 人正在输入...
                    </p>
                  )}
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}

/**
 * 简化的连接状态指示器
 */
export function ConnectionDot({ connectionState }: { connectionState: string }) {
  const getStatusColor = () => {
    switch (connectionState) {
      case 'connected':
        return 'bg-green-500';
      case 'connecting':
        return 'bg-yellow-500 animate-pulse';
      case 'disconnected':
        return 'bg-gray-400';
      case 'error':
        return 'bg-red-500';
      default:
        return 'bg-gray-400';
    }
  };

  const getTooltip = () => {
    switch (connectionState) {
      case 'connected':
        return '实时连接正常';
      case 'connecting':
        return '正在连接...';
      case 'disconnected':
        return '已断开连接';
      case 'error':
        return '连接出错';
      default:
        return '未连接';
    }
  };

  return (
    <div
      className={cn('w-2 h-2 rounded-full', getStatusColor())}
      title={getTooltip()}
    />
  );
}

/**
 * 打字指示器动画组件
 */
export function TypingIndicator({ isVisible }: { isVisible: boolean }) {
  if (!isVisible) return null;

  return (
    <div className="flex items-center gap-1 text-blue-600 dark:text-blue-400">
      <div className="flex gap-1">
        <div className="w-2 h-2 bg-current rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
        <div className="w-2 h-2 bg-current rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
        <div className="w-2 h-2 bg-current rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
      </div>
      <span className="text-sm ml-2">正在输入...</span>
    </div>
  );
}

/**
 * 实时评论徽章
 */
export function RealtimeBadge({ show }: { show: boolean }) {
  if (!show) return null;

  return (
    <div className="inline-flex items-center gap-1 px-2 py-1 bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 text-xs rounded-full">
      <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
      <span>实时</span>
    </div>
  );
}