'use client';

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { AlertTriangle, RefreshCw } from 'lucide-react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error?: Error;
}

export class BlogErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Blog Error Boundary caught an error:', error, errorInfo);
    
    // 调用外部错误处理回调
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined });
  };

  render() {
    if (this.state.hasError) {
      // 如果提供了自定义错误UI，使用它
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // 默认错误UI
      return (
        <div className="min-h-[200px] flex items-center justify-center p-6">
          <div className="text-center max-w-md">
            <AlertTriangle className="w-12 h-12 text-orange-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
              出现了一些问题
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4 text-sm">
              抱歉，页面加载时遇到错误。您可以尝试刷新页面或稍后再试。
            </p>
            <button
              onClick={this.handleRetry}
              className="inline-flex items-center gap-2 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
            >
              <RefreshCw className="w-4 h-4" />
              重试
            </button>
            {process.env.NODE_ENV === 'development' && this.state.error && (
              <details className="mt-4 text-left">
                <summary className="cursor-pointer text-sm text-gray-500 dark:text-gray-400">
                  错误详情 (开发模式)
                </summary>
                <pre className="mt-2 p-3 bg-gray-100 dark:bg-gray-800 rounded text-xs overflow-auto">
                  {this.state.error.stack}
                </pre>
              </details>
            )}
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// 专门用于交互功能的错误边界
export function InteractionsErrorBoundary({ children }: { children: ReactNode }) {
  return (
    <BlogErrorBoundary
      fallback={
        <div className="text-center py-4">
          <p className="text-sm text-gray-500 dark:text-gray-400">
            交互功能暂时不可用
          </p>
        </div>
      }
      onError={(error) => {
        // 只在控制台记录，不影响主要内容
        console.warn('Interactions feature error:', error);
      }}
    >
      {children}
    </BlogErrorBoundary>
  );
}

// 专门用于评论功能的错误边界
export function CommentsErrorBoundary({ children }: { children: ReactNode }) {
  return (
    <BlogErrorBoundary
      fallback={
        <div className="text-center py-8">
          <AlertTriangle className="w-8 h-8 text-orange-500 mx-auto mb-2" />
          <p className="text-gray-600 dark:text-gray-400">
            评论功能暂时不可用，请稍后再试
          </p>
        </div>
      }
      onError={(error) => {
        console.error('Comments feature error:', error);
      }}
    >
      {children}
    </BlogErrorBoundary>
  );
}