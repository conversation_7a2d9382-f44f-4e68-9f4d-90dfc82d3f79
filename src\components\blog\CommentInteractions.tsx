'use client';

import React, { useState, useEffect } from 'react';
import { ThumbsUp, Reply, Flag } from 'lucide-react';
import { createRoot } from 'react-dom/client';
import { cn } from '@/lib/utils';
import { showToast } from '@/components/ui/toast';
import { ReplyForm } from './ReplyForm';

interface CommentInteractionsProps {
  postId: string;
  userId?: string;
  className?: string;
}

interface LikedCommentsStorage {
  [commentId: string]: {
    isLiked: boolean;
    timestamp: number;
  };
}

/**
 * 评论交互增强组件 - 纯客户端功能
 * 为SSR渲染的评论内容添加交互功能
 * 实现乐观更新和本地存储备份机制
 */
export function CommentInteractions({ postId, userId, className }: CommentInteractionsProps) {
  const [likedComments, setLikedComments] = useState<LikedCommentsStorage>({});
  const [isInitialized, setIsInitialized] = useState(false);

  // 初始化本地存储状态
  useEffect(() => {
    try {
      const stored = localStorage.getItem('likedComments');
      if (stored) {
        const parsed = JSON.parse(stored);
        setLikedComments(parsed);
      }
    } catch (error) {
      console.error('Failed to load liked comments from localStorage:', error);
    }
    setIsInitialized(true);
  }, []);

  // 同步状态到本地存储
  useEffect(() => {
    if (isInitialized && Object.keys(likedComments).length > 0) {
      try {
        localStorage.setItem('likedComments', JSON.stringify(likedComments));
      } catch (error) {
        console.error('Failed to save liked comments to localStorage:', error);
      }
    }
  }, [likedComments, isInitialized]);

  // 处理点赞操作
  const handleLikeComment = async (commentId: string) => {
    const currentState = likedComments[commentId];
    const newIsLiked = !currentState?.isLiked;

    // 获取当前点赞数量进行乐观更新
    const countElement = document.querySelector(`[data-comment-id="${commentId}"] .like-count`);
    const currentCount = parseInt(countElement?.textContent || '0');
    const newCount = newIsLiked ? currentCount + 1 : Math.max(currentCount - 1, 0);

    // 乐观更新本地状态
    const newState = {
      isLiked: newIsLiked,
      timestamp: Date.now(),
    };

    setLikedComments(prev => ({
      ...prev,
      [commentId]: newState,
    }));

    // 立即更新UI显示（乐观更新）
    updateCommentLikeUI(commentId, newIsLiked);
    updateCommentLikeCount(commentId, newCount);

    try {
      const response = await fetch(`/api/blog/${postId}/comments/${commentId}/like`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId }),
      });

      const data = await response.json();

      if (data.success) {
        // 用服务器返回的准确数据更新UI（通常与乐观更新一致）
        updateCommentLikeCount(commentId, data.data.likeCount);

        // 静默成功，不显示toast（避免过多提示）
        // showToast({
        //   message: newIsLiked ? '点赞成功' : '取消点赞',
        //   type: 'success',
        //   duration: 2000,
        //   position: 'bottom-center'
        // });
      } else {
        // API失败时回滚状态
        setLikedComments(prev => {
          const updated = { ...prev };
          if (currentState) {
            updated[commentId] = currentState;
          } else {
            delete updated[commentId];
          }
          return updated;
        });

        // 回滚UI状态
        updateCommentLikeUI(commentId, currentState?.isLiked || false);
        updateCommentLikeCount(commentId, currentCount);

        showToast({
          message: data.error || '操作失败，请重试',
          type: 'error',
          duration: 3000,
          position: 'top-center'
        });
      }
    } catch (error) {
      console.error('Failed to like comment:', error);

      // 网络错误时回滚UI状态，但保持本地状态
      updateCommentLikeUI(commentId, currentState?.isLiked || false);
      updateCommentLikeCount(commentId, currentCount);

      showToast({
        message: '网络错误，请稍后重试',
        type: 'error',
        duration: 3000,
        position: 'top-center'
      });
    }
  };

  // 更新评论点赞UI状态
  const updateCommentLikeUI = (commentId: string, isLiked: boolean) => {
    const button = document.querySelector(`[data-comment-id="${commentId}"] .comment-like-button`);
    if (button) {
      const icon = button.querySelector('.like-icon');
      const text = button.querySelector('.like-text');
      
      if (icon) {
        if (isLiked) {
          icon.classList.add('text-red-500', 'fill-current');
          icon.classList.remove('text-gray-500', 'dark:text-gray-400');
        } else {
          icon.classList.remove('text-red-500', 'fill-current');
          icon.classList.add('text-gray-500', 'dark:text-gray-400');
        }
      }
      
      button.classList.toggle('text-red-500', isLiked);
      button.classList.toggle('text-gray-500', !isLiked);
      button.classList.toggle('dark:text-gray-400', !isLiked);
    }
  };

  // 更新评论点赞数量
  const updateCommentLikeCount = (commentId: string, count: number) => {
    const countElement = document.querySelector(`[data-comment-id="${commentId}"] .like-count`);
    if (countElement) {
      countElement.textContent = count.toString();
    }
  };

  // 处理回复操作
  const handleReplyComment = (commentId: string) => {
    const replyFormContainer = document.querySelector(`[data-reply-form="${commentId}"]`);
    if (!replyFormContainer) return;

    const isHidden = replyFormContainer.classList.contains('hidden');
    
    if (isHidden) {
      // 显示回复表单
      replyFormContainer.classList.remove('hidden');
      
      // 动态渲染回复表单
      const commentElement = document.querySelector(`[data-comment-id="${commentId}"]`);
      const userNameElement = commentElement?.querySelector('[itemProp="name"]');
      const userName = userNameElement?.textContent || '匿名用户';
      
      // 清空容器并渲染新的回复表单
      replyFormContainer.innerHTML = '';
      const root = createRoot(replyFormContainer);
      root.render(
        <ReplyForm
          postId={postId}
          parentCommentId={commentId}
          parentUserName={userName}
          userId={userId}
          allowGuests={true}
          onCancel={() => {
            replyFormContainer.classList.add('hidden');
            root.unmount();
          }}
          onReplyAdded={() => {
            // 回复成功后的处理将在ReplyForm组件内完成
          }}
        />
      );
    } else {
      // 隐藏回复表单
      replyFormContainer.classList.add('hidden');
      replyFormContainer.innerHTML = '';
    }
  };

  // 处理举报操作
  const handleReportComment = (commentId: string) => {
    showToast({
      message: '举报功能暂未开放',
      type: 'info',
      duration: 2000,
      position: 'top-center'
    });
  };

  // 为所有评论按钮添加事件监听器
  useEffect(() => {
    if (!isInitialized) return;

    const handleClick = (event: Event) => {
      const target = event.target as HTMLElement;
      const button = target.closest('button');
      if (!button) return;

      const commentElement = button.closest('[data-comment-id]');
      if (!commentElement) return;

      const commentId = commentElement.getAttribute('data-comment-id');
      if (!commentId) return;

      if (button.classList.contains('comment-like-button')) {
        event.preventDefault();
        handleLikeComment(commentId);
      } else if (button.classList.contains('comment-reply-button')) {
        event.preventDefault();
        handleReplyComment(commentId);
      } else if (button.classList.contains('comment-report-button')) {
        event.preventDefault();
        handleReportComment(commentId);
      }
    };

    document.addEventListener('click', handleClick);
    
    // 初始化已点赞状态
    Object.entries(likedComments).forEach(([commentId, state]) => {
      if (state.isLiked) {
        updateCommentLikeUI(commentId, true);
      }
    });

    return () => {
      document.removeEventListener('click', handleClick);
    };
  }, [isInitialized, likedComments, postId, userId]);

  // 这个组件不渲染任何内容，只是添加交互功能
  return null;
}

/**
 * 评论交互按钮组件 - 用于SSR渲染时的占位按钮
 */
export function CommentActionButtons({ 
  commentId, 
  likeCount = 0,
  className 
}: { 
  commentId: string; 
  likeCount?: number;
  className?: string;
}) {
  return (
    <div className={cn('flex items-center gap-4 text-sm', className)}>
      <button
        className="comment-like-button flex items-center gap-1 transition-colors text-gray-500 dark:text-gray-400 hover:text-red-500"
        data-comment-id={commentId}
        data-initial-count={likeCount}
      >
        <ThumbsUp className="like-icon w-4 h-4" />
        <span className="like-count">{likeCount}</span>
      </button>

      <button
        className="comment-reply-button text-gray-500 dark:text-gray-400 hover:text-blue-500 transition-colors"
        data-comment-id={commentId}
      >
        <Reply className="w-4 h-4 inline mr-1" />
        回复
      </button>

      <button
        className="comment-report-button text-gray-500 dark:text-gray-400 hover:text-orange-500 transition-colors"
        data-comment-id={commentId}
      >
        <Flag className="w-4 h-4 inline mr-1" />
        举报
      </button>
    </div>
  );
}