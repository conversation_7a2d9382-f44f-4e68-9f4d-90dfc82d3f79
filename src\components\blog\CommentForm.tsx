'use client';

import React, { useState } from 'react';
import { Send } from 'lucide-react';
import { cn } from '@/lib/utils';
import { showToast } from '@/components/ui/toast';

interface CommentFormProps {
  postId: string;
  userId?: string;
  allowGuests?: boolean;
  className?: string;
  onCommentAdded?: () => void;
}

/**
 * 评论表单组件 - 纯客户端交互
 * 专门用于评论提交，不包含评论显示逻辑
 */
export function CommentForm({
  postId,
  userId,
  allowGuests = true,
  className,
  onCommentAdded
}: CommentFormProps) {
  const [newComment, setNewComment] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [guestName, setGuestName] = useState('');
  const [guestEmail, setGuestEmail] = useState('');

  const submitComment = async (content: string) => {
    if (!content.trim()) return;
    
    if (!userId && !allowGuests) {
      showToast({
        message: '请先登录后再发表评论',
        type: 'error',
        duration: 3000,
        position: 'top-center'
      });
      return;
    }

    if (!userId && (!guestName.trim() || !guestEmail.trim())) {
      showToast({
        message: '请填写姓名和邮箱',
        type: 'error',
        duration: 3000,
        position: 'top-center'
      });
      return;
    }

    setIsSubmitting(true);
    
    try {
      const response = await fetch(`/api/blog/${postId}/comments`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: content.trim(),
          userId: userId || null,
          guestName: !userId ? guestName.trim() : undefined,
          guestEmail: !userId ? guestEmail.trim() : undefined,
        }),
      });

      const data = await response.json();
      
      if (data.success) {
        // 显示成功提示
        const isGuest = !userId;
        const successMessage = isGuest 
          ? '评论发表成功！感谢您的参与！' 
          : '评论发表成功！';
        
        showToast({
          message: successMessage,
          type: 'success',
          duration: 3000,
          position: 'top-center'
        });
        
        // 清空表单
        setNewComment('');
        if (!userId) {
          setGuestName('');
          setGuestEmail('');
        }
        
        // 通知父组件刷新评论
        if (onCommentAdded) {
          onCommentAdded();
        }
        
        // 刷新页面以显示新评论（简单方案）
        setTimeout(() => {
          window.location.reload();
        }, 1000);
        
      } else {
        throw new Error(data.error || '评论发表失败');
      }
    } catch (error) {
      console.error('Failed to submit comment:', error);
      showToast({
        message: error instanceof Error ? error.message : '评论发表失败，请重试',
        type: 'error',
        duration: 3000,
        position: 'top-center'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className={cn('p-6 bg-gray-50 dark:bg-gray-800 rounded-xl', className)}>
      <h4 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
        发表评论
      </h4>
      
      {!userId && allowGuests && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <input
            type="text"
            placeholder="您的姓名 *"
            value={guestName}
            onChange={(e) => setGuestName(e.target.value)}
            className="px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg
              bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100
              focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
          <input
            type="email"
            placeholder="您的邮箱 *"
            value={guestEmail}
            onChange={(e) => setGuestEmail(e.target.value)}
            className="px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg
              bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100
              focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
      )}
      
      <textarea
        value={newComment}
        onChange={(e) => setNewComment(e.target.value)}
        placeholder="写下您的评论..."
        className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg
          bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100
          focus:ring-2 focus:ring-blue-500 focus:border-transparent
          resize-none"
        rows={4}
      />
      
      <div className="flex justify-end items-center mt-4">
        <button
          onClick={() => submitComment(newComment)}
          disabled={isSubmitting || !newComment.trim() || (!userId && (!guestName.trim() || !guestEmail.trim()))}
          className="px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600
            disabled:opacity-50 disabled:cursor-not-allowed
            flex items-center gap-2 transition-colors"
        >
          {isSubmitting ? (
            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
          ) : (
            <Send className="w-4 h-4" />
          )}
          发表评论
        </button>
      </div>
    </div>
  );
}
