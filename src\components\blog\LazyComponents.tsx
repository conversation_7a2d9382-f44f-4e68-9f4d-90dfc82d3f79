'use client';

import { lazy, Suspense, useState, useEffect, useRef } from 'react';
import { MessageCircle, Share2, BookmarkIcon, Heart } from 'lucide-react';

// 懒加载评论表单
const CommentForm = lazy(() => import('./CommentForm').then(module => ({ default: module.CommentForm })));

// 懒加载社交分享对话框
const SocialShare = lazy(() => import('./SocialShare').then(module => ({ default: module.SocialShare })));

// 懒加载复杂的表情选择器（如果有的话）
const EmojiPicker = lazy(() => import('./EmojiPicker').catch(() => ({ default: () => null })));

// 骨架加载组件
export function CommentFormSkeleton() {
  return (
    <div className="animate-pulse">
      <div className="mb-4">
        <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-24 mb-2"></div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div className="h-12 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
          <div className="h-12 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
        </div>
      </div>
      <div className="h-24 bg-gray-200 dark:bg-gray-700 rounded-lg mb-4"></div>
      <div className="flex justify-end">
        <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded-lg w-24"></div>
      </div>
    </div>
  );
}

export function SocialShareSkeleton() {
  return (
    <div className="animate-pulse">
      <div className="grid grid-cols-4 gap-3">
        {Array.from({ length: 4 }).map((_, i) => (
          <div key={i} className="flex flex-col items-center gap-2">
            <div className="w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
            <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-12"></div>
          </div>
        ))}
      </div>
    </div>
  );
}

export function InteractionsSkeleton() {
  return (
    <div className="flex items-center gap-4 animate-pulse">
      {[Heart, BookmarkIcon, MessageCircle, Share2].map((Icon, i) => (
        <div key={i} className="flex items-center gap-2">
          <Icon className="w-4 h-4 text-gray-300 dark:text-gray-600" />
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-8"></div>
        </div>
      ))}
    </div>
  );
}

// 懒加载包装器组件
export function LazyCommentForm(props: any) {
  return (
    <Suspense fallback={<CommentFormSkeleton />}>
      <CommentForm {...props} />
    </Suspense>
  );
}

export function LazySocialShare(props: any) {
  return (
    <Suspense fallback={<SocialShareSkeleton />}>
      <SocialShare {...props} />
    </Suspense>
  );
}

export function LazyEmojiPicker(props: any) {
  return (
    <Suspense fallback={<div className="w-6 h-6 animate-spin border-2 border-gray-300 border-t-blue-500 rounded-full"></div>}>
      <EmojiPicker {...props} />
    </Suspense>
  );
}

// 通用的懒加载容器
interface LazyContainerProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  threshold?: number;
  rootMargin?: string;
}

export function LazyContainer({ 
  children, 
  fallback, 
  threshold = 0.1, 
  rootMargin = '50px' 
}: LazyContainerProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [hasLoaded, setHasLoaded] = useState(false);
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !hasLoaded) {
          setIsVisible(true);
          setHasLoaded(true);
          observer.disconnect();
        }
      },
      {
        threshold,
        rootMargin,
      }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => observer.disconnect();
  }, [threshold, rootMargin, hasLoaded]);

  return (
    <div ref={ref}>
      {isVisible ? children : (fallback || <div className="h-20"></div>)}
    </div>
  );
}

