'use client';

import React, { useState, useEffect } from 'react';
import { Heart, Bookmark, MessageCircle, Eye, Share2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useBlogInteractions, useBlogStats, useBlogUserState, useBlogActions } from '@/components/providers/blog-interactions-provider';

interface ArticleInteractionsProps {
  className?: string;
  variant?: 'floating' | 'inline' | 'compact';
  showCounts?: boolean;
  onShare?: (platform: string) => void;
}



export function ArticleInteractions({
  className,
  variant = 'inline',
  showCounts = true,
  onShare,
}: ArticleInteractionsProps) {
  const { state } = useBlogInteractions();
  const stats = useBlogStats();
  const userState = useBlogUserState();
  const { handleLike, handleBookmark, handleShare } = useBlogActions();
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // 浮动模式的可见性控制
    if (variant === 'floating') {
      const handleScroll = () => {
        const scrollTop = window.pageYOffset;
        setIsVisible(scrollTop > 300);
      };

      window.addEventListener('scroll', handleScroll, { passive: true });
      return () => window.removeEventListener('scroll', handleScroll);
    }
    
    // 其他variant时返回undefined
    return undefined;
  }, [variant]);

  // 处理分享，结合外部回调
  const handleShareWithCallback = async (platform: string = 'general') => {
    await handleShare(platform);
    
    // 触发外部分享回调
    if (onShare) {
      onShare(platform);
    }
  };







  const interactions = [
    {
      icon: Heart,
      count: stats.likes,
      isActive: userState.isLiked,
      onClick: handleLike,
      label: 'Like',
      activeColor: 'text-red-500',
      activeBg: 'bg-red-50 dark:bg-red-900/20',
      disabled: state.loading.like,
      isLoading: state.loading.like,
      hoverScale: true,
    },
    {
      icon: Bookmark,
      count: null,
      isActive: userState.isBookmarked,
      onClick: handleBookmark,
      label: 'Bookmark',
      activeColor: 'text-yellow-500',
      activeBg: 'bg-yellow-50 dark:bg-yellow-900/20',
      disabled: state.loading.bookmark,
      isLoading: state.loading.bookmark,
      hoverScale: true,
    },
    {
      icon: MessageCircle,
      count: stats.comments,
      isActive: false,
      onClick: () => {
        // 滚动到评论区域
        const commentsSection = document.getElementById('comments');
        if (commentsSection) {
          commentsSection.scrollIntoView({ behavior: 'smooth' });
        }
      },
      label: 'Comments',
      activeColor: 'text-blue-500',
      activeBg: 'bg-blue-50 dark:bg-blue-900/20',
      disabled: false,
      isLoading: false,
      hoverScale: true,
    },
    {
      icon: Share2,
      count: stats.shares,
      isActive: false,
      onClick: () => handleShareWithCallback('general'),
      label: 'Share',
      activeColor: 'text-green-500',
      activeBg: 'bg-green-50 dark:bg-green-900/20',
      disabled: false,
      isLoading: false,
      hoverScale: true,
    },
  ];

  if (variant === 'floating') {
    return (
      <div
        className={cn(
          'transition-all duration-300',
          isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4',
          className
        )}
      >
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-3">
          <div className="flex flex-col gap-3">
            {/* 浏览量显示 */}
            <div className="flex flex-col items-center gap-1">
              <Eye className="w-4 h-4 text-gray-400" />
              <span className="text-xs text-gray-500 dark:text-gray-400">
                {stats.views}
              </span>
            </div>
            
            <div className="border-t border-gray-200 dark:border-gray-700 pt-3">
              <div className="flex flex-col gap-3">
                {interactions.map((interaction, index) => {
                  const IconComponent = interaction.icon;
                  return (
                    <button
                      key={index}
                      onClick={interaction.onClick}
                      disabled={interaction.disabled}
                      className={cn(
                        'group relative flex flex-col items-center gap-1 p-2 rounded-lg transition-all duration-300',
                        'transform hover:scale-105 active:scale-95',
                        interaction.disabled && 'opacity-50 cursor-not-allowed hover:scale-100',
                        interaction.isActive
                          ? cn(
                              interaction.activeBg,
                              interaction.activeColor,
                              'shadow-md ring-2 ring-opacity-20',
                              interaction.activeColor.includes('red') && 'ring-red-300',
                              interaction.activeColor.includes('yellow') && 'ring-yellow-300',
                              interaction.activeColor.includes('blue') && 'ring-blue-300',
                              interaction.activeColor.includes('green') && 'ring-green-300'
                            )
                          : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 hover:text-gray-800 dark:hover:text-gray-200'
                      )}
                      title={interaction.label}
                    >
                      {/* 加载状态指示器 */}
                      {interaction.isLoading && (
                        <div className="absolute inset-0 flex items-center justify-center bg-white/80 dark:bg-gray-800/80 rounded-lg">
                          <div className="w-3 h-3 border-2 border-current border-t-transparent rounded-full animate-spin opacity-60" />
                        </div>
                      )}

                      {/* 图标 - 激活时使用填充版本 */}
                      <IconComponent
                        className={cn(
                          'w-4 h-4 transition-all duration-200',
                          interaction.isActive && (
                            interaction.label === 'Like' || interaction.label === 'Bookmark'
                          ) && 'fill-current'
                        )}
                      />

                      {/* 计数显示 */}
                      {interaction.count !== null && showCounts && (
                        <span className={cn(
                          'text-xs font-medium transition-all duration-200',
                          interaction.isActive && 'font-semibold'
                        )}>
                          {interaction.count}
                        </span>
                      )}

                      {/* 点击波纹效果 */}
                      <div className="absolute inset-0 rounded-lg overflow-hidden">
                        <div className="absolute inset-0 bg-current opacity-0 group-active:opacity-10 transition-opacity duration-150" />
                      </div>
                    </button>
                  );
                })}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (variant === 'compact') {
    return (
      <div className={cn('flex items-center gap-2', className)}>
        {interactions.map((interaction, index) => {
          const IconComponent = interaction.icon;
          return (
            <button
              key={index}
              onClick={interaction.onClick}
              disabled={interaction.disabled}
              className={cn(
                'group relative flex items-center gap-1 px-2 py-1 rounded-lg transition-all duration-300 text-sm',
                'transform hover:scale-105 active:scale-95',
                interaction.disabled && 'opacity-50 cursor-not-allowed hover:scale-100',
                interaction.isActive
                  ? cn(
                      interaction.activeBg,
                      interaction.activeColor,
                      'shadow-sm ring-1 ring-opacity-20',
                      interaction.activeColor.includes('red') && 'ring-red-300',
                      interaction.activeColor.includes('yellow') && 'ring-yellow-300',
                      interaction.activeColor.includes('blue') && 'ring-blue-300',
                      interaction.activeColor.includes('green') && 'ring-green-300'
                    )
                  : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700'
              )}
            >
              {/* 加载状态指示器 */}
              {interaction.isLoading && (
                <div className="absolute inset-0 flex items-center justify-center bg-white/80 dark:bg-gray-800/80 rounded-lg">
                  <div className="w-3 h-3 border-2 border-current border-t-transparent rounded-full animate-spin opacity-60" />
                </div>
              )}

              {/* 图标 - 激活时使用填充版本 */}
              <IconComponent
                className={cn(
                  'w-4 h-4 transition-all duration-200',
                  interaction.isActive && (
                    interaction.label === 'Like' || interaction.label === 'Bookmark'
                  ) && 'fill-current'
                )}
              />

              {/* 计数显示 */}
              {interaction.count !== null && showCounts && (
                <span className={cn(
                  'transition-all duration-200',
                  interaction.isActive && 'font-semibold'
                )}>
                  {interaction.count}
                </span>
              )}
            </button>
          );
        })}
      </div>
    );
  }

  // Default inline variant
  return (
    <div className={cn('flex items-center justify-between py-4 border-y border-mystical-200 dark:border-dark-700', className)}>
      <div className="flex items-center gap-4">
        {interactions.map((interaction, index) => {
          const IconComponent = interaction.icon;
          return (
            <button
              key={index}
              onClick={interaction.onClick}
              disabled={interaction.disabled}
              className={cn(
                'group relative flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-300',
                'transform hover:scale-105 active:scale-95',
                interaction.disabled && 'opacity-50 cursor-not-allowed hover:scale-100',
                interaction.isActive
                  ? cn(
                      interaction.activeBg,
                      interaction.activeColor,
                      'shadow-sm ring-1 ring-opacity-20',
                      interaction.activeColor.includes('red') && 'ring-red-300',
                      interaction.activeColor.includes('yellow') && 'ring-yellow-300',
                      interaction.activeColor.includes('blue') && 'ring-blue-300',
                      interaction.activeColor.includes('green') && 'ring-green-300'
                    )
                  : 'text-mystical-600 dark:text-mystical-400 hover:bg-mystical-100 dark:hover:bg-dark-700'
              )}
            >
              {/* 加载状态指示器 */}
              {interaction.isLoading && (
                <div className="absolute inset-0 flex items-center justify-center bg-white/80 dark:bg-gray-800/80 rounded-lg">
                  <div className="w-3 h-3 border-2 border-current border-t-transparent rounded-full animate-spin opacity-60" />
                </div>
              )}

              {/* 图标 - 激活时使用填充版本 */}
              <IconComponent
                className={cn(
                  'w-4 h-4 transition-all duration-200',
                  interaction.isActive && (
                    interaction.label === 'Like' || interaction.label === 'Bookmark'
                  ) && 'fill-current'
                )}
              />

              <span className={cn(
                'text-sm font-medium transition-all duration-200',
                interaction.isActive && 'font-semibold'
              )}>
                {interaction.label}
              </span>

              {interaction.count !== null && showCounts && (
                <span className={cn(
                  'text-sm transition-all duration-200',
                  interaction.isActive && 'font-semibold'
                )}>
                  ({interaction.count})
                </span>
              )}
            </button>
          );
        })}
      </div>
      
      {/* 浏览量显示 */}
      <div className="flex items-center gap-1 text-mystical-500 dark:text-mystical-400">
        <Eye className="w-4 h-4" />
        <span className="text-sm">{stats.views} views</span>
      </div>
    </div>
  );
}
