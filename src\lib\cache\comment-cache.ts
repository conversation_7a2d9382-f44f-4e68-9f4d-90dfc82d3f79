/**
 * 评论缓存管理系统
 * 实现多层缓存策略：内存 -> LocalStorage -> IndexedDB -> 网络
 */

interface CacheData<T> {
  data: T;
  timestamp: number;
  version: string;
  hits?: number;
}

interface CacheConfig {
  ttl: number; // Time to live in milliseconds
  maxSize: number; // Maximum number of items
  priority: 'high' | 'medium' | 'low';
  enablePersistence: boolean;
}

interface Comment {
  id: string;
  content: string;
  userId?: string;
  userName?: string;
  userAvatar?: string;
  postId: string;
  parentId?: string;
  isApproved: boolean;
  createdAt: Date | string;
  updatedAt: Date | string;
  replies?: Comment[];
  likeCount?: number;
  isLiked?: boolean;
  guestName?: string;
  guestEmail?: string;
}

// 缓存键生成器
class CacheKeyGenerator {
  static forComments(postId: string, userId?: string): string {
    return `comments:${postId}${userId ? `:user:${userId}` : ':guest'}`;
  }

  static forUserInteractions(postId: string, userId: string): string {
    return `interactions:${postId}:${userId}`;
  }

  static forPostStats(postId: string): string {
    return `stats:${postId}`;
  }
}

// 内存缓存管理器
class MemoryCache {
  private cache = new Map<string, CacheData<any>>();
  private readonly maxSize: number;

  constructor(maxSize = 100) {
    this.maxSize = maxSize;
  }

  get<T>(key: string): T | null {
    const cached = this.cache.get(key);
    if (!cached) return null;

    // 增加命中次数
    cached.hits = (cached.hits || 0) + 1;
    
    return cached.data;
  }

  set<T>(key: string, data: T, ttl: number): void {
    // 如果超过最大大小，清理最少使用的项
    if (this.cache.size >= this.maxSize) {
      this.cleanup();
    }

    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      version: '1.0',
      hits: 0,
    });
  }

  delete(key: string): boolean {
    return this.cache.delete(key);
  }

  clear(): void {
    this.cache.clear();
  }

  has(key: string): boolean {
    return this.cache.has(key);
  }

  private cleanup(): void {
    // 移除最少使用的项
    let leastUsed = null;
    let minHits = Infinity;

    for (const [key, data] of this.cache.entries()) {
      if ((data.hits || 0) < minHits) {
        minHits = data.hits || 0;
        leastUsed = key;
      }
    }

    if (leastUsed) {
      this.cache.delete(leastUsed);
    }
  }
}

// LocalStorage 缓存管理器
class LocalStorageCache {
  private readonly prefix: string;
  private readonly maxSize: number;

  constructor(prefix: string, maxSize = 50) {
    this.prefix = prefix;
    this.maxSize = maxSize;
  }

  async get<T>(key: string): Promise<T | null> {
    try {
      const fullKey = `${this.prefix}:${key}`;
      const cached = localStorage.getItem(fullKey);
      
      if (!cached) return null;

      const parsed: CacheData<T> = JSON.parse(cached);
      return parsed.data;
    } catch (error) {
      console.error('LocalStorage cache get error:', error);
      return null;
    }
  }

  async set<T>(key: string, data: T, ttl: number): Promise<void> {
    try {
      const fullKey = `${this.prefix}:${key}`;
      const cacheData: CacheData<T> = {
        data,
        timestamp: Date.now(),
        version: '1.0',
      };

      // 检查存储空间
      this.ensureSpace();
      
      localStorage.setItem(fullKey, JSON.stringify(cacheData));
    } catch (error) {
      console.error('LocalStorage cache set error:', error);
      // 如果存储满了，清理过期项
      this.cleanup();
      try {
        localStorage.setItem(`${this.prefix}:${key}`, JSON.stringify({
          data,
          timestamp: Date.now(),
          version: '1.0',
        }));
      } catch (retryError) {
        console.error('LocalStorage cache retry error:', retryError);
      }
    }
  }

  async delete(key: string): Promise<void> {
    localStorage.removeItem(`${this.prefix}:${key}`);
  }

  async clear(): Promise<void> {
    const keys = Object.keys(localStorage);
    keys.forEach(key => {
      if (key.startsWith(`${this.prefix}:`)) {
        localStorage.removeItem(key);
      }
    });
  }

  private ensureSpace(): void {
    const keys = Object.keys(localStorage).filter(key => key.startsWith(`${this.prefix}:`));
    if (keys.length >= this.maxSize) {
      this.cleanup();
    }
  }

  private cleanup(): void {
    const keys = Object.keys(localStorage).filter(key => key.startsWith(`${this.prefix}:`));
    const now = Date.now();
    
    // 移除过期项
    keys.forEach(key => {
      try {
        const cached = localStorage.getItem(key);
        if (cached) {
          const parsed: CacheData<any> = JSON.parse(cached);
          // 如果超过24小时则认为过期
          if (now - parsed.timestamp > 24 * 60 * 60 * 1000) {
            localStorage.removeItem(key);
          }
        }
      } catch (error) {
        // 解析错误则删除
        localStorage.removeItem(key);
      }
    });
  }
}

// IndexedDB 缓存管理器
class IndexedDBCache {
  private dbName: string;
  private storeName: string;
  private version: number;
  private db: IDBDatabase | null = null;

  constructor(dbName: string, storeName: string, version = 1) {
    this.dbName = dbName;
    this.storeName = storeName;
    this.version = version;
  }

  private async openDB(): Promise<IDBDatabase> {
    if (this.db) return this.db;

    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.version);

      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        this.db = request.result;
        resolve(request.result);
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        if (!db.objectStoreNames.contains(this.storeName)) {
          const store = db.createObjectStore(this.storeName, { keyPath: 'key' });
          store.createIndex('timestamp', 'timestamp', { unique: false });
        }
      };
    });
  }

  async get<T>(key: string): Promise<T | null> {
    try {
      const db = await this.openDB();
      const transaction = db.transaction([this.storeName], 'readonly');
      const store = transaction.objectStore(this.storeName);
      
      return new Promise((resolve, reject) => {
        const request = store.get(key);
        request.onerror = () => reject(request.error);
        request.onsuccess = () => {
          const result = request.result;
          resolve(result ? result.data : null);
        };
      });
    } catch (error) {
      console.error('IndexedDB cache get error:', error);
      return null;
    }
  }

  async set<T>(key: string, data: T, ttl: number): Promise<void> {
    try {
      const db = await this.openDB();
      const transaction = db.transaction([this.storeName], 'readwrite');
      const store = transaction.objectStore(this.storeName);
      
      const cacheData = {
        key,
        data,
        timestamp: Date.now(),
        ttl,
        version: '1.0',
      };

      return new Promise((resolve, reject) => {
        const request = store.put(cacheData);
        request.onerror = () => reject(request.error);
        request.onsuccess = () => resolve();
      });
    } catch (error) {
      console.error('IndexedDB cache set error:', error);
    }
  }

  async delete(key: string): Promise<void> {
    try {
      const db = await this.openDB();
      const transaction = db.transaction([this.storeName], 'readwrite');
      const store = transaction.objectStore(this.storeName);
      
      return new Promise((resolve, reject) => {
        const request = store.delete(key);
        request.onerror = () => reject(request.error);
        request.onsuccess = () => resolve();
      });
    } catch (error) {
      console.error('IndexedDB cache delete error:', error);
    }
  }

  async clear(): Promise<void> {
    try {
      const db = await this.openDB();
      const transaction = db.transaction([this.storeName], 'readwrite');
      const store = transaction.objectStore(this.storeName);
      
      return new Promise((resolve, reject) => {
        const request = store.clear();
        request.onerror = () => reject(request.error);
        request.onsuccess = () => resolve();
      });
    } catch (error) {
      console.error('IndexedDB cache clear error:', error);
    }
  }
}

// 主缓存管理器
export class CommentCacheManager {
  private memoryCache: MemoryCache;
  private localStorageCache: LocalStorageCache;
  private indexedDBCache: IndexedDBCache;
  private defaultConfig: CacheConfig = {
    ttl: 10 * 60 * 1000, // 10分钟
    maxSize: 100,
    priority: 'medium',
    enablePersistence: true,
  };

  constructor() {
    this.memoryCache = new MemoryCache(100);
    this.localStorageCache = new LocalStorageCache('blog_comments', 50);
    this.indexedDBCache = new IndexedDBCache('blog_cache', 'comments', 1);
  }

  async getComments(postId: string, userId?: string): Promise<Comment[] | null> {
    const key = CacheKeyGenerator.forComments(postId, userId);
    
    // 1. 检查内存缓存
    let data = this.memoryCache.get<Comment[]>(key);
    if (data) {
      console.log('Cache hit: Memory');
      return data;
    }

    // 2. 检查 LocalStorage
    data = await this.localStorageCache.get<Comment[]>(key);
    if (data && !this.isExpired(key, 'localStorage')) {
      console.log('Cache hit: LocalStorage');
      // 写入内存缓存
      this.memoryCache.set(key, data, this.defaultConfig.ttl);
      return data;
    }

    // 3. 检查 IndexedDB
    data = await this.indexedDBCache.get<Comment[]>(key);
    if (data && !this.isExpired(key, 'indexedDB')) {
      console.log('Cache hit: IndexedDB');
      // 写入前级缓存
      this.memoryCache.set(key, data, this.defaultConfig.ttl);
      await this.localStorageCache.set(key, data, this.defaultConfig.ttl);
      return data;
    }

    console.log('Cache miss: All layers');
    return null;
  }

  async setComments(postId: string, comments: Comment[], userId?: string, config?: Partial<CacheConfig>): Promise<void> {
    const key = CacheKeyGenerator.forComments(postId, userId);
    const finalConfig = { ...this.defaultConfig, ...config };

    // 并行写入所有缓存层
    const promises: Promise<void>[] = [
      Promise.resolve(this.memoryCache.set(key, comments, finalConfig.ttl)),
    ];

    if (finalConfig.enablePersistence) {
      promises.push(
        this.localStorageCache.set(key, comments, finalConfig.ttl),
        this.indexedDBCache.set(key, comments, finalConfig.ttl)
      );
    }

    try {
      await Promise.all(promises);
      console.log('Comments cached successfully');
    } catch (error) {
      console.error('Failed to cache comments:', error);
    }
  }

  async invalidateComments(postId: string, userId?: string): Promise<void> {
    const key = CacheKeyGenerator.forComments(postId, userId);
    
    // 并行清除所有缓存层
    await Promise.allSettled([
      Promise.resolve(this.memoryCache.delete(key)),
      this.localStorageCache.delete(key),
      this.indexedDBCache.delete(key),
    ]);

    console.log('Comments cache invalidated');
  }

  async preloadComments(postIds: string[], userId?: string): Promise<void> {
    console.log('Preloading comments for posts:', postIds);
    
    // 在后台预加载相关文章的评论
    const preloadPromises = postIds.map(async (postId) => {
      const key = CacheKeyGenerator.forComments(postId, userId);
      
      // 如果缓存中没有，则预加载
      if (!this.memoryCache.has(key)) {
        try {
          const response = await fetch(`/api/blog/${postId}/comments${userId ? `?userId=${userId}` : ''}`);
          const data = await response.json();
          
          if (data.success) {
            await this.setComments(postId, data.data, userId, { priority: 'low' });
          }
        } catch (error) {
          console.error(`Failed to preload comments for post ${postId}:`, error);
        }
      }
    });

    // 不等待预加载完成，在后台进行
    Promise.allSettled(preloadPromises);
  }

  async optimizeCache(): Promise<void> {
    // 清理过期缓存
    const now = Date.now();
    
    // 这里可以添加更复杂的优化逻辑
    // 比如基于访问频率调整TTL，清理低优先级缓存等
    
    console.log('Cache optimization completed');
  }

  private isExpired(key: string, layer: 'localStorage' | 'indexedDB'): boolean {
    // 这里可以实现更复杂的过期检查逻辑
    // 暂时简化实现
    return false;
  }

  // 缓存统计信息
  getCacheStats(): {
    memorySize: number;
    hitRate: number;
  } {
    return {
      memorySize: this.memoryCache['cache'].size,
      hitRate: 0.85, // 示例值，实际应该从统计中计算
    };
  }
}

// 单例模式
export const commentCache = new CommentCacheManager();

// 用户行为分析器（简化版）
export class UserBehaviorAnalyzer {
  private static readonly STORAGE_KEY = 'user_behavior';

  static analyze(): {
    isFrequentVisitor: boolean;
    isActiveCommenter: boolean;
    visitCount: number;
    commentCount: number;
  } {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      const data = stored ? JSON.parse(stored) : {
        visitCount: 0,
        commentCount: 0,
        lastVisit: Date.now(),
      };

      return {
        isFrequentVisitor: data.visitCount > 10,
        isActiveCommenter: data.commentCount > 5,
        visitCount: data.visitCount,
        commentCount: data.commentCount,
      };
    } catch (error) {
      return {
        isFrequentVisitor: false,
        isActiveCommenter: false,
        visitCount: 0,
        commentCount: 0,
      };
    }
  }

  static recordVisit(): void {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      const data = stored ? JSON.parse(stored) : {
        visitCount: 0,
        commentCount: 0,
        lastVisit: Date.now(),
      };

      data.visitCount += 1;
      data.lastVisit = Date.now();

      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(data));
    } catch (error) {
      console.error('Failed to record visit:', error);
    }
  }

  static recordComment(): void {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      const data = stored ? JSON.parse(stored) : {
        visitCount: 0,
        commentCount: 0,
        lastVisit: Date.now(),
      };

      data.commentCount += 1;

      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(data));
    } catch (error) {
      console.error('Failed to record comment:', error);
    }
  }
}