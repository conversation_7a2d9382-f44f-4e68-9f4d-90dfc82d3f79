import React from 'react';
import { MessageCircle, User } from 'lucide-react';
import { cn } from '@/lib/utils';
import { CommentActionButtons } from './CommentInteractions';

interface Comment {
  id: string;
  content: string;
  userId?: string;
  userName?: string;
  userAvatar?: string;
  postId: string;
  parentId?: string;
  isApproved: boolean;
  createdAt: string;
  updatedAt: string;
  replies?: Comment[];
  likeCount?: number;
  isLiked?: boolean;
  guestName?: string;
  guestEmail?: string;
}

interface CommentsSSRProps {
  comments: Comment[];
  postId: string;
  className?: string;
}

/**
 * SSR友好的评论显示组件
 * 纯展示组件，确保评论内容对搜索引擎可见
 * 包含结构化数据标记和语义化HTML
 */
export function CommentsSSR({ comments, postId, className }: CommentsSSRProps) {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  const renderComment = (comment: Comment, isReply = false) => {
    const displayName = comment.userName || comment.guestName || '匿名用户';
    const createdAt = new Date(comment.createdAt);
    
    return (
      <article
        key={comment.id}
        data-comment-id={comment.id}
        itemScope
        itemType="https://schema.org/Comment"
        className={cn(
          'border-b border-gray-100 dark:border-gray-800 pb-6 mb-6',
          isReply && 'ml-8 border-l-2 border-gray-200 dark:border-gray-700 pl-4'
        )}
      >
        {/* 评论头部 */}
        <header className="flex items-start gap-3 mb-3">
          {/* 用户头像 */}
          <div className="flex-shrink-0">
            {comment.userAvatar ? (
              <img
                src={comment.userAvatar}
                alt={displayName}
                itemProp="author"
                className="w-10 h-10 rounded-full object-cover"
              />
            ) : (
              <div className="w-10 h-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
                <User className="w-5 h-5 text-gray-600 dark:text-gray-400" />
              </div>
            )}
          </div>

          {/* 评论信息 */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <span 
                className="font-medium text-gray-900 dark:text-gray-100"
                itemProp="author"
                itemScope
                itemType="https://schema.org/Person"
              >
                <span itemProp="name">{displayName}</span>
              </span>
              {!comment.userId && (
                <span className="text-xs px-2 py-0.5 bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 rounded-full">
                  游客
                </span>
              )}
            </div>
            <time 
              className="text-sm text-gray-500 dark:text-gray-400"
              itemProp="datePublished"
              dateTime={createdAt.toISOString()}
            >
              {formatDate(comment.createdAt)}
            </time>
          </div>
        </header>

        {/* 评论内容 */}
        <div className="ml-13">
          <div 
            className="prose prose-sm max-w-none text-gray-700 dark:text-gray-300"
            itemProp="text"
          >
            <p className="whitespace-pre-wrap break-words">{comment.content}</p>
          </div>

          {/* 评论交互按钮 - SSR骨架，客户端增强 */}
          <div className="mt-3">
            <CommentActionButtons 
              commentId={comment.id}
              likeCount={comment.likeCount || 0}
            />
          </div>
        </div>

        {/* 回复表单占位 - 客户端动态显示 */}
        <div 
          data-reply-form={comment.id}
          className="hidden mt-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg"
        >
          {/* 回复表单将由客户端组件动态填充 */}
        </div>

        {/* 回复评论 */}
        {comment.replies && comment.replies.length > 0 && (
          <div className="mt-6">
            {comment.replies.map((reply) => renderComment(reply, true))}
          </div>
        )}
      </article>
    );
  };

  if (!comments || comments.length === 0) {
    return (
      <div className={cn('text-center py-12', className)}>
        <MessageCircle className="w-12 h-12 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
        <p className="text-gray-500 dark:text-gray-400">暂无评论，来发表第一条评论吧！</p>
      </div>
    );
  }

  return (
    <section 
      className={cn('space-y-6', className)}
      itemScope
      itemType="https://schema.org/CommentAction"
    >
      {/* 评论标题 */}
      <header className="flex items-center gap-2 pb-4 border-b border-gray-200 dark:border-gray-700">
        <MessageCircle className="w-5 h-5 text-gray-600 dark:text-gray-400" />
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
          评论 ({comments.length})
        </h3>
      </header>

      {/* 评论列表 */}
      <div className="space-y-6" role="list">
        {comments.map((comment) => renderComment(comment))}
      </div>
    </section>
  );
}

/**
 * 评论数量统计组件 - SSR友好
 */
export function CommentCount({ count }: { count: number }) {
  return (
    <div className="flex items-center gap-1 text-gray-600 dark:text-gray-400">
      <MessageCircle className="w-4 h-4" />
      <span className="text-sm">{count} 条评论</span>
    </div>
  );
}
