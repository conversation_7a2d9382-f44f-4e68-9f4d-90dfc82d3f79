# WebSocket 实时功能配置指南

## 🚀 快速开始

### 方案一：禁用WebSocket功能（推荐用于开发）

在项目根目录创建 `.env.local` 文件：

```bash
# 禁用实时功能
NEXT_PUBLIC_ENABLE_REALTIME=false
```

或者简单地不设置该环境变量（默认禁用）。

### 方案二：启用WebSocket功能

```bash
# 启用实时功能
NEXT_PUBLIC_ENABLE_REALTIME=true
```

## 📋 WebSocket 实现选项

### 1. 自定义服务器 (推荐用于生产)

使用 `ws` 库创建WebSocket服务器：

```bash
npm install ws @types/ws
```

创建 `server.js`:

```javascript
const { createServer } = require('http');
const { parse } = require('url');
const next = require('next');
const WebSocket = require('ws');

const dev = process.env.NODE_ENV !== 'production';
const hostname = 'localhost';
const port = 3000;

const app = next({ dev, hostname, port });
const handle = app.getRequestHandler();

app.prepare().then(() => {
  const server = createServer(async (req, res) => {
    try {
      const parsedUrl = parse(req.url, true);
      await handle(req, res, parsedUrl);
    } catch (err) {
      console.error('Error occurred handling', req.url, err);
      res.statusCode = 500;
      res.end('internal server error');
    }
  });

  // WebSocket服务器
  const wss = new WebSocket.Server({ server, path: '/api/ws' });

  const rooms = new Map(); // postId -> Set of connections

  wss.on('connection', (ws, req) => {
    const url = parse(req.url, true);
    const { postId, userId } = url.query;

    console.log(`New connection: postId=${postId}, userId=${userId}`);

    // 加入房间
    if (!rooms.has(postId)) {
      rooms.set(postId, new Set());
    }
    rooms.get(postId).add(ws);

    // 发送欢迎消息
    ws.send(JSON.stringify({
      type: 'user_count',
      data: { count: rooms.get(postId).size }
    }));

    // 处理消息
    ws.on('message', (message) => {
      try {
        const data = JSON.parse(message);
        
        // 广播给同一文章的其他用户
        const room = rooms.get(postId);
        if (room) {
          room.forEach(client => {
            if (client !== ws && client.readyState === WebSocket.OPEN) {
              client.send(JSON.stringify(data));
            }
          });
        }
      } catch (error) {
        console.error('Message parsing error:', error);
      }
    });

    // 处理断开连接
    ws.on('close', () => {
      const room = rooms.get(postId);
      if (room) {
        room.delete(ws);
        if (room.size === 0) {
          rooms.delete(postId);
        } else {
          // 通知剩余用户
          room.forEach(client => {
            if (client.readyState === WebSocket.OPEN) {
              client.send(JSON.stringify({
                type: 'user_count',
                data: { count: room.size }
              }));
            }
          });
        }
      }
    });
  });

  server.listen(port, (err) => {
    if (err) throw err;
    console.log(`> Ready on http://${hostname}:${port}`);
  });
});
```

修改 `package.json`:

```json
{
  "scripts": {
    "dev": "node server.js",
    "build": "next build",
    "start": "NODE_ENV=production node server.js"
  }
}
```

### 2. 使用第三方服务

#### Pusher

```bash
npm install pusher pusher-js
```

```typescript
// lib/pusher.ts
import Pusher from 'pusher';

export const pusher = new Pusher({
  appId: process.env.PUSHER_APP_ID!,
  key: process.env.PUSHER_KEY!,
  secret: process.env.PUSHER_SECRET!,
  cluster: process.env.PUSHER_CLUSTER!,
  useTLS: true
});
```

#### Ably

```bash
npm install ably
```

#### Socket.io

```bash
npm install socket.io socket.io-client
```

### 3. Vercel部署考虑

Vercel默认不支持WebSocket。对于生产环境，推荐：

1. 使用Pusher、Ably等第三方服务
2. 部署到支持WebSocket的平台（Railway、Render等）
3. 使用Server-Sent Events (SSE) 作为替代

## 🔧 配置选项

### 环境变量

```bash
# .env.local
NEXT_PUBLIC_ENABLE_REALTIME=true          # 启用实时功能
NEXT_PUBLIC_WS_URL=ws://localhost:3000    # WebSocket服务器地址

# 如果使用Pusher
PUSHER_APP_ID=your_app_id
PUSHER_KEY=your_key
PUSHER_SECRET=your_secret
PUSHER_CLUSTER=your_cluster
NEXT_PUBLIC_PUSHER_KEY=your_key
NEXT_PUBLIC_PUSHER_CLUSTER=your_cluster
```

## 🐛 故障排除

### 常见错误

1. **WebSocket connection failed**
   - 确保WebSocket服务器正在运行
   - 检查防火墙设置
   - 验证URL格式

2. **Connection refused**
   - 检查端口是否被占用
   - 确认服务器启动成功

3. **Deployment issues**
   - Vercel: 考虑使用第三方服务
   - 自托管: 确保WebSocket端口开放

### 调试模式

启用详细日志：

```bash
DEBUG=ws* npm run dev
```

## 📊 功能对比

| 方案 | 复杂度 | 成本 | 可扩展性 | 部署难度 |
|------|--------|------|----------|----------|
| 禁用 | 低 | 免费 | - | 容易 |
| 自定义服务器 | 中 | 低 | 中 | 中等 |
| Pusher | 低 | 付费 | 高 | 容易 |
| Ably | 低 | 付费 | 高 | 容易 |
| Socket.io | 中 | 低 | 中 | 中等 |

## 🎯 推荐方案

- **开发环境**: 禁用WebSocket功能
- **小项目**: 自定义WebSocket服务器
- **大项目**: Pusher或Ably
- **Vercel部署**: 第三方服务或SSE