/**
 * WebSocket 实时评论更新管理器
 * 实现评论的实时同步和冲突解决
 */

interface RealtimeComment {
  id: string;
  content: string;
  userId?: string;
  userName?: string;
  postId: string;
  parentId?: string;
  createdAt: string;
  action: 'create' | 'update' | 'delete' | 'like';
  metadata?: {
    likeCount?: number;
    isLiked?: boolean;
    timestamp: number;
  };
}

interface WebSocketMessage {
  type: 'comment_update' | 'user_count' | 'typing' | 'presence';
  postId: string;
  data: any;
  timestamp: number;
}

interface TypingIndicator {
  userId: string;
  userName: string;
  isTyping: boolean;
  timestamp: number;
}

interface UserPresence {
  userId: string;
  userName: string;
  lastSeen: number;
  isActive: boolean;
}

// WebSocket 连接管理器
export class WebSocketManager {
  private static instance: WebSocketManager;
  private socket: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectInterval = 1000;
  private listeners: Map<string, Set<Function>> = new Map();
  private postId: string | null = null;
  private userId: string | null = null;
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private connectionState: 'connecting' | 'connected' | 'disconnected' | 'error' = 'disconnected';

  static getInstance(): WebSocketManager {
    if (!WebSocketManager.instance) {
      WebSocketManager.instance = new WebSocketManager();
    }
    return WebSocketManager.instance;
  }

  // 连接WebSocket
  connect(postId: string, userId?: string): Promise<void> {
    return new Promise((resolve, reject) => {
      // 如果已经连接到同一个文章，直接返回
      if (this.socket?.readyState === WebSocket.OPEN && this.postId === postId) {
        resolve();
        return;
      }

      this.postId = postId;
      this.userId = userId || null;
      this.connectionState = 'connecting';

      try {
        // 使用环境变量配置WebSocket URL
        const wsUrl = process.env.NEXT_PUBLIC_WS_URL || 
                     `${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.host}/api/ws`;
        
        this.socket = new WebSocket(`${wsUrl}?postId=${postId}&userId=${userId || 'guest'}`);

        this.socket.onopen = () => {
          console.log('WebSocket connected');
          this.connectionState = 'connected';
          this.reconnectAttempts = 0;
          this.startHeartbeat();
          this.emit('connection', { status: 'connected' });
          resolve();
        };

        this.socket.onmessage = (event) => {
          try {
            const message: WebSocketMessage = JSON.parse(event.data);
            this.handleMessage(message);
          } catch (error) {
            console.error('Failed to parse WebSocket message:', error);
          }
        };

        this.socket.onclose = (event) => {
          console.log('WebSocket disconnected:', event.code, event.reason);
          this.connectionState = 'disconnected';
          this.stopHeartbeat();
          this.emit('connection', { status: 'disconnected', code: event.code });
          
          // 自动重连
          if (!event.wasClean && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.scheduleReconnect();
          }
        };

        this.socket.onerror = (error) => {
          console.error('WebSocket error:', error);
          this.connectionState = 'error';
          this.emit('connection', { status: 'error', error });
          reject(error);
        };

      } catch (error) {
        this.connectionState = 'error';
        reject(error);
      }
    });
  }

  // 断开连接
  disconnect(): void {
    if (this.socket) {
      this.socket.close(1000, 'User disconnect');
      this.socket = null;
    }
    this.stopHeartbeat();
    this.connectionState = 'disconnected';
    this.postId = null;
    this.userId = null;
  }

  // 发送消息
  send(message: Partial<WebSocketMessage>): void {
    if (this.socket?.readyState === WebSocket.OPEN && this.postId) {
      const fullMessage: WebSocketMessage = {
        type: message.type || 'comment_update',
        postId: this.postId,
        data: message.data || {},
        timestamp: Date.now(),
        ...message,
      };
      
      this.socket.send(JSON.stringify(fullMessage));
    } else {
      console.warn('WebSocket not connected, message not sent:', message);
    }
  }

  // 添加事件监听器
  on(event: string, callback: Function): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set());
    }
    this.listeners.get(event)!.add(callback);
  }

  // 移除事件监听器
  off(event: string, callback: Function): void {
    this.listeners.get(event)?.delete(callback);
  }

  // 触发事件
  private emit(event: string, data: any): void {
    this.listeners.get(event)?.forEach(callback => {
      try {
        callback(data);
      } catch (error) {
        console.error('Event listener error:', error);
      }
    });
  }

  // 处理收到的消息
  private handleMessage(message: WebSocketMessage): void {
    switch (message.type) {
      case 'comment_update':
        this.emit('comment_update', message.data);
        break;
      
      case 'user_count':
        this.emit('user_count', message.data);
        break;
      
      case 'typing':
        this.emit('typing', message.data);
        break;
      
      case 'presence':
        this.emit('presence', message.data);
        break;
      
      default:
        console.warn('Unknown message type:', message.type);
    }
  }

  // 计划重连
  private scheduleReconnect(): void {
    this.reconnectAttempts++;
    const delay = Math.min(this.reconnectInterval * Math.pow(2, this.reconnectAttempts), 30000);
    
    console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts})`);
    
    setTimeout(() => {
      if (this.postId) {
        this.connect(this.postId, this.userId || undefined);
      }
    }, delay);
  }

  // 开始心跳
  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      if (this.socket?.readyState === WebSocket.OPEN) {
        this.socket.send(JSON.stringify({ type: 'ping' }));
      }
    }, 30000); // 30秒心跳
  }

  // 停止心跳
  private stopHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  // 获取连接状态
  getConnectionState(): string {
    return this.connectionState;
  }

  // 发送打字指示器
  sendTypingIndicator(isTyping: boolean): void {
    if (!this.userId) return; // 游客不发送打字指示器
    
    this.send({
      type: 'typing',
      data: {
        userId: this.userId,
        isTyping,
        timestamp: Date.now(),
      },
    });
  }

  // 发送用户在线状态
  sendPresence(isActive: boolean): void {
    if (!this.userId) return;
    
    this.send({
      type: 'presence',
      data: {
        userId: this.userId,
        isActive,
        timestamp: Date.now(),
      },
    });
  }
}

// 实时评论管理器
export class RealtimeCommentManager {
  private wsManager: WebSocketManager;
  private commentUpdateQueue: RealtimeComment[] = [];
  private isProcessingQueue = false;
  private updateCallbacks: Set<(comment: RealtimeComment) => void> = new Set();
  private typingUsers: Map<string, TypingIndicator> = new Map();
  private onlineUsers: Map<string, UserPresence> = new Map();

  constructor() {
    this.wsManager = WebSocketManager.getInstance();
    this.setupEventListeners();
  }

  // 设置事件监听器
  private setupEventListeners(): void {
    this.wsManager.on('comment_update', (data: RealtimeComment) => {
      this.queueCommentUpdate(data);
    });

    this.wsManager.on('typing', (data: TypingIndicator) => {
      this.handleTypingUpdate(data);
    });

    this.wsManager.on('presence', (data: UserPresence) => {
      this.handlePresenceUpdate(data);
    });

    this.wsManager.on('user_count', (data: { count: number, users: UserPresence[] }) => {
      this.updateOnlineUsers(data.users);
    });
  }

  // 连接到文章
  async connectToPost(postId: string, userId?: string): Promise<void> {
    try {
      await this.wsManager.connect(postId, userId);
      console.log('Connected to realtime comments for post:', postId);
    } catch (error) {
      console.error('Failed to connect to realtime comments:', error);
      // 失败不影响正常功能，静默处理
    }
  }

  // 断开连接
  disconnect(): void {
    this.wsManager.disconnect();
    this.commentUpdateQueue = [];
    this.typingUsers.clear();
    this.onlineUsers.clear();
  }

  // 添加评论更新回调
  onCommentUpdate(callback: (comment: RealtimeComment) => void): () => void {
    this.updateCallbacks.add(callback);
    
    // 返回取消函数
    return () => {
      this.updateCallbacks.delete(callback);
    };
  }

  // 队列化评论更新
  private queueCommentUpdate(comment: RealtimeComment): void {
    this.commentUpdateQueue.push(comment);
    this.processQueue();
  }

  // 处理更新队列
  private async processQueue(): Promise<void> {
    if (this.isProcessingQueue || this.commentUpdateQueue.length === 0) {
      return;
    }

    this.isProcessingQueue = true;

    while (this.commentUpdateQueue.length > 0) {
      const comment = this.commentUpdateQueue.shift()!;
      
      // 通知所有回调
      this.updateCallbacks.forEach(callback => {
        try {
          callback(comment);
        } catch (error) {
          console.error('Comment update callback error:', error);
        }
      });

      // 短暂延迟避免UI阻塞
      await new Promise(resolve => setTimeout(resolve, 50));
    }

    this.isProcessingQueue = false;
  }

  // 处理打字指示器更新
  private handleTypingUpdate(data: TypingIndicator): void {
    if (data.isTyping) {
      this.typingUsers.set(data.userId, data);
      
      // 5秒后自动清除打字状态
      setTimeout(() => {
        const current = this.typingUsers.get(data.userId);
        if (current && current.timestamp === data.timestamp) {
          this.typingUsers.delete(data.userId);
          this.notifyTypingChange();
        }
      }, 5000);
    } else {
      this.typingUsers.delete(data.userId);
    }
    
    this.notifyTypingChange();
  }

  // 处理在线状态更新
  private handlePresenceUpdate(data: UserPresence): void {
    if (data.isActive) {
      this.onlineUsers.set(data.userId, data);
    } else {
      this.onlineUsers.delete(data.userId);
    }
  }

  // 更新在线用户列表
  private updateOnlineUsers(users: UserPresence[]): void {
    this.onlineUsers.clear();
    users.forEach(user => {
      this.onlineUsers.set(user.userId, user);
    });
  }

  // 通知打字状态变化
  private notifyTypingChange(): void {
    // 这里可以触发UI更新
    console.log('Typing users:', Array.from(this.typingUsers.values()));
  }

  // 发送打字指示器
  sendTypingIndicator(isTyping: boolean): void {
    this.wsManager.sendTypingIndicator(isTyping);
  }

  // 获取在线用户数
  getOnlineUserCount(): number {
    return this.onlineUsers.size;
  }

  // 获取打字用户
  getTypingUsers(): TypingIndicator[] {
    return Array.from(this.typingUsers.values());
  }

  // 获取连接状态
  getConnectionState(): string {
    return this.wsManager.getConnectionState();
  }
}

// 单例实例
export const realtimeCommentManager = new RealtimeCommentManager();

// React Hook for realtime comments
export function useRealtimeComments(postId: string, userId?: string) {
  const [connectionState, setConnectionState] = React.useState('disconnected');
  const [onlineCount, setOnlineCount] = React.useState(0);
  const [typingUsers, setTypingUsers] = React.useState<TypingIndicator[]>([]);

  React.useEffect(() => {
    // 连接到实时评论
    realtimeCommentManager.connectToPost(postId, userId);

    // 监听连接状态
    const wsManager = WebSocketManager.getInstance();
    const handleConnection = (data: any) => {
      setConnectionState(data.status);
    };

    wsManager.on('connection', handleConnection);

    // 定期更新状态
    const interval = setInterval(() => {
      setOnlineCount(realtimeCommentManager.getOnlineUserCount());
      setTypingUsers(realtimeCommentManager.getTypingUsers());
    }, 1000);

    // 清理
    return () => {
      wsManager.off('connection', handleConnection);
      clearInterval(interval);
      realtimeCommentManager.disconnect();
    };
  }, [postId, userId]);

  return {
    connectionState,
    onlineCount,
    typingUsers,
    sendTypingIndicator: realtimeCommentManager.sendTypingIndicator.bind(realtimeCommentManager),
    onCommentUpdate: realtimeCommentManager.onCommentUpdate.bind(realtimeCommentManager),
  };
}

// React import for hooks
import React from 'react';