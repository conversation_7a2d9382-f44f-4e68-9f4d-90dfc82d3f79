'use client';

import React, { useState } from 'react';
import { Send } from 'lucide-react';
import { cn } from '@/lib/utils';
import { showToast } from '@/components/ui/toast';

interface ReplyFormProps {
  postId: string;
  parentCommentId: string;
  parentUserName: string;
  userId?: string;
  allowGuests?: boolean;
  className?: string;
  onReplyAdded?: (reply: any) => void;
  onCancel?: () => void;
}

/**
 * 回复表单组件 - 专门处理评论回复
 * 支持游客回复和登录用户回复
 */
export function ReplyForm({
  postId,
  parentCommentId,
  parentUserName,
  userId,
  allowGuests = true,
  className,
  onReplyAdded,
  onCancel
}: ReplyFormProps) {
  const [replyContent, setReplyContent] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [guestName, setGuestName] = useState('');
  const [guestEmail, setGuestEmail] = useState('');

  const submitReply = async () => {
    if (!replyContent.trim()) {
      showToast({
        message: '请输入回复内容',
        type: 'error',
        duration: 2000,
        position: 'top-center'
      });
      return;
    }
    
    if (!userId && !allowGuests) {
      showToast({
        message: '请先登录后再发表回复',
        type: 'error',
        duration: 3000,
        position: 'top-center'
      });
      return;
    }

    if (!userId && (!guestName.trim() || !guestEmail.trim())) {
      showToast({
        message: '请填写姓名和邮箱',
        type: 'error',
        duration: 3000,
        position: 'top-center'
      });
      return;
    }

    setIsSubmitting(true);
    
    try {
      const response = await fetch(`/api/blog/${postId}/comments`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: replyContent.trim(),
          parentId: parentCommentId,
          userId: userId || null,
          guestName: !userId ? guestName.trim() : undefined,
          guestEmail: !userId ? guestEmail.trim() : undefined,
        }),
      });

      const data = await response.json();
      
      if (data.success) {
        const isGuest = !userId;
        const successMessage = isGuest 
          ? '回复发表成功！感谢您的参与！' 
          : '回复发表成功！';
        
        showToast({
          message: successMessage,
          type: 'success',
          duration: 3000,
          position: 'top-center'
        });
        
        // 清空表单
        setReplyContent('');
        if (!userId) {
          setGuestName('');
          setGuestEmail('');
        }
        
        // 通知父组件
        if (onReplyAdded) {
          onReplyAdded(data.data);
        }
        
        // 简单方案：刷新页面显示新回复
        setTimeout(() => {
          window.location.reload();
        }, 1000);
        
      } else {
        throw new Error(data.error || '回复发表失败');
      }
    } catch (error) {
      console.error('Failed to submit reply:', error);
      showToast({
        message: error instanceof Error ? error.message : '回复发表失败，请重试',
        type: 'error',
        duration: 3000,
        position: 'top-center'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className={cn('p-4 bg-gray-50 dark:bg-gray-800 rounded-lg', className)}>
      <div className="mb-3">
        <span className="text-sm text-gray-600 dark:text-gray-400">
          回复 <span className="font-medium text-gray-900 dark:text-gray-100">{parentUserName}</span>
        </span>
      </div>
      
      {!userId && allowGuests && (
        <div className="grid grid-cols-2 gap-3 mb-3">
          <input
            type="text"
            placeholder="您的姓名"
            value={guestName}
            onChange={(e) => setGuestName(e.target.value)}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg
              bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100
              focus:ring-2 focus:ring-blue-500 focus:border-transparent
              text-sm"
          />
          <input
            type="email"
            placeholder="您的邮箱"
            value={guestEmail}
            onChange={(e) => setGuestEmail(e.target.value)}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg
              bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100
              focus:ring-2 focus:ring-blue-500 focus:border-transparent
              text-sm"
          />
        </div>
      )}
      
      <textarea
        value={replyContent}
        onChange={(e) => setReplyContent(e.target.value)}
        placeholder={`回复 ${parentUserName}...`}
        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg
          bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100
          focus:ring-2 focus:ring-blue-500 focus:border-transparent
          resize-none text-sm"
        rows={3}
        autoFocus
      />
      
      <div className="flex justify-end gap-2 mt-3">
        <button
          onClick={onCancel}
          className="px-3 py-1.5 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
        >
          取消
        </button>
        <button
          onClick={submitReply}
          disabled={isSubmitting || !replyContent.trim() || (!userId && (!guestName.trim() || !guestEmail.trim()))}
          className="px-4 py-1.5 bg-blue-500 text-white rounded-lg hover:bg-blue-600
            disabled:opacity-50 disabled:cursor-not-allowed
            flex items-center gap-2 text-sm transition-colors"
        >
          {isSubmitting ? (
            <div className="w-3 h-3 border-2 border-white border-t-transparent rounded-full animate-spin" />
          ) : (
            <Send className="w-3 h-3" />
          )}
          发送回复
        </button>
      </div>
    </div>
  );
}